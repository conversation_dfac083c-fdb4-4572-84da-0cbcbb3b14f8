<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Glossary Store</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        .product-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-item {
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .product-item img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛒 Glossary Store - System Test</h1>
        
        <div class="test-section info">
            <h3>📊 System Status</h3>
            <p><strong>Backend Status:</strong> <span id="backend-status">Checking...</span></p>
            <p><strong>Database Status:</strong> <span id="database-status">Checking...</span></p>
            <p><strong>Products Loaded:</strong> <span id="products-count">0</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Test Functions</h3>
            <button onclick="testBackend()">Test Backend Connection</button>
            <button onclick="testProducts()">Load Products</button>
            <button onclick="testCart()">Test Cart Functions</button>
            <button onclick="testSearch()">Test Search</button>
        </div>

        <div class="test-section">
            <h3>📦 Available Products</h3>
            <div id="products-display" class="product-list">
                <p>Click "Load Products" to see available items</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🛒 Cart Test</h3>
            <div id="cart-display">
                <p>Cart is empty</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script>
        // Test configuration
        const API_BASE_URL = 'http://localhost:3000/api';
        let testResults = [];

        // Sample products for testing
        const sampleProducts = {
            1: { name: "Fresh Orange", price: 12.99, image: "image/product-1.png", unit: "kg" },
            2: { name: "Fresh Apple", price: 18.99, image: "image/product-9.jpg", unit: "kg" },
            3: { name: "Fresh Avocado", price: 25.99, image: "image/product-6.png", unit: "kg" },
            4: { name: "Fresh Onion", price: 8.99, image: "image/product-2.png", unit: "kg" },
            5: { name: "Fresh Potato", price: 6.99, image: "image/product-5.png", unit: "kg" },
            7: { name: "Basmati Rice", price: 45.99, image: "image/product-7.png", unit: "5kg bag" },
            8: { name: "Whole Wheat Flour", price: 35.99, image: "image/product-10.jpg", unit: "5kg bag" },
            10: { name: "Fresh Milk", price: 4.99, image: "image/product-12.jpeg", unit: "1L bottle" },
            12: { name: "Olive Oil", price: 24.99, image: "image/product-14.png", unit: "500ml bottle" },
            15: { name: "Whole Wheat Bread", price: 5.99, image: "image/cart-img-1.png", unit: "loaf" }
        };

        let cart = [];

        // Test backend connection
        async function testBackend() {
            addTestResult('Testing backend connection...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/products`);
                if (response.ok) {
                    document.getElementById('backend-status').textContent = '✅ Connected';
                    document.getElementById('backend-status').style.color = 'green';
                    addTestResult('✅ Backend connection successful', 'success');
                    return true;
                } else {
                    throw new Error('Backend not responding');
                }
            } catch (error) {
                document.getElementById('backend-status').textContent = '❌ Disconnected';
                document.getElementById('backend-status').style.color = 'red';
                addTestResult('❌ Backend connection failed: Using local data', 'error');
                return false;
            }
        }

        // Test products loading
        async function testProducts() {
            addTestResult('Testing product loading...', 'info');
            
            const backendConnected = await testBackend();
            let products = [];

            if (backendConnected) {
                try {
                    const response = await fetch(`${API_BASE_URL}/products`);
                    products = await response.json();
                    addTestResult(`✅ Loaded ${products.length} products from database`, 'success');
                } catch (error) {
                    addTestResult('❌ Failed to load from database, using local products', 'error');
                    products = Object.values(sampleProducts).map((p, i) => ({...p, id: i + 1}));
                }
            } else {
                products = Object.values(sampleProducts).map((p, i) => ({...p, id: i + 1}));
                addTestResult(`📦 Using ${products.length} local products`, 'info');
            }

            document.getElementById('products-count').textContent = products.length;
            displayProducts(products);
        }

        // Display products
        function displayProducts(products) {
            const container = document.getElementById('products-display');
            container.innerHTML = '';

            products.forEach(product => {
                const productDiv = document.createElement('div');
                productDiv.className = 'product-item';
                productDiv.innerHTML = `
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2RkZCIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Tm8gSW1hZ2U8L3RleHQ+PC9zdmc+'">
                    <h4>${product.name}</h4>
                    <p>$${product.price} / ${product.unit || 'item'}</p>
                    <button onclick="addToCart(${product.id})">Add to Cart</button>
                `;
                container.appendChild(productDiv);
            });
        }

        // Test cart functionality
        function testCart() {
            addTestResult('Testing cart functionality...', 'info');
            
            // Test adding items
            addToCart(1);
            addToCart(7);
            addToCart(1); // Add duplicate to test quantity increase
            
            addTestResult(`✅ Cart test completed. Items in cart: ${cart.length}`, 'success');
            displayCart();
        }

        // Add to cart function
        function addToCart(productId) {
            const product = sampleProducts[productId];
            if (!product) {
                addTestResult(`❌ Product ${productId} not found`, 'error');
                return;
            }

            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: productId,
                    name: product.name,
                    price: product.price,
                    unit: product.unit,
                    quantity: 1
                });
            }

            addTestResult(`✅ Added ${product.name} to cart`, 'success');
            displayCart();
        }

        // Display cart
        function displayCart() {
            const container = document.getElementById('cart-display');
            if (cart.length === 0) {
                container.innerHTML = '<p>Cart is empty</p>';
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            
            container.innerHTML = `
                <h4>Cart Items (${cart.length})</h4>
                ${cart.map(item => `
                    <div style="border: 1px solid #ddd; padding: 10px; margin: 5px; border-radius: 5px;">
                        <strong>${item.name}</strong><br>
                        $${item.price} / ${item.unit} × ${item.quantity} = $${(item.price * item.quantity).toFixed(2)}
                        <button onclick="removeFromCart(${item.id})" style="background: #f44336; margin-left: 10px;">Remove</button>
                    </div>
                `).join('')}
                <div style="margin-top: 15px; font-weight: bold; font-size: 18px;">
                    Total: $${total.toFixed(2)}
                </div>
            `;
        }

        // Remove from cart
        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            addTestResult(`🗑️ Removed item from cart`, 'info');
            displayCart();
        }

        // Test search functionality
        function testSearch() {
            addTestResult('Testing search functionality...', 'info');
            
            const searchTerms = ['rice', 'milk', 'oil'];
            searchTerms.forEach(term => {
                const results = Object.values(sampleProducts).filter(p => 
                    p.name.toLowerCase().includes(term.toLowerCase())
                );
                addTestResult(`🔍 Search "${term}": Found ${results.length} results`, 'info');
            });
        }

        // Add test result
        function addTestResult(message, type) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // Initialize tests on page load
        window.onload = function() {
            addTestResult('🚀 Test page loaded successfully', 'success');
            testBackend();
        };
    </script>
</body>
</html>
