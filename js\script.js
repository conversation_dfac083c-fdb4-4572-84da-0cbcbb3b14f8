let searchForm = document.querySelector('.search-form');

document.querySelector('#search-btn').onclick = () =>
{
	searchForm.classList.toggle('active');
	shoppingCart.classList.remove('active');
	loginForm.classList.remove('active');
	navbar.classList.remove('active');

	// Focus on search input when opened
	if (searchForm.classList.contains('active')) {
		setTimeout(() => {
			const searchInput = document.getElementById('search-box');
			if (searchInput) {
				searchInput.focus();
			}
		}, 100);
	}
}


let shoppingCart = document.querySelector('.shopping-cart');

document.querySelector('#cart-btn').onclick = () =>
{
	shoppingCart.classList.toggle('active');
	searchForm.classList.remove('active');
    loginForm.classList.remove('active');
	navbar.classList.remove('active');
}



let loginForm = document.querySelector('.login-form');

document.querySelector('#login-btn').onclick = () =>
{
	loginForm.classList.toggle('active');
	shoppingCart.classList.remove('active');
	searchForm.classList.remove('active');
	navbar.classList.remove('active');
}



let navbar = document.querySelector('.navbar');

document.querySelector('#menu-btn').onclick = () =>
{
	navbar.classList.toggle('active');
	loginForm.classList.remove('active');
	shoppingCart.classList.remove('active');
	searchForm.classList.remove('active');
	
}



window.onscroll = () =>
{
	searchForm.classList.remove('active');
	shoppingCart.classList.remove('active');
	loginForm.classList.remove('active');
	navbar.classList.remove('active');
}



 var swiper = new Swiper(".product-slider", {
      loop:true,
      spaceBetween: 20,

      autoplay:{
      	delay: 7500,
      	disableOnInteraction: false,
      },

      breakpoints: {
        0: {
          slidesPerView: 1,
        },
        768: {
          slidesPerView: 2,
        },
        1020: {
          slidesPerView: 3,
        },
      },
    });

// Enhanced functionality for the website

// API base URL - change this when backend is running
const API_BASE_URL = 'http://localhost:3000/api';

// Check if backend is available
let backendAvailable = false;

async function checkBackend() {
    try {
        const response = await fetch(`${API_BASE_URL}/products`);
        backendAvailable = response.ok;
        console.log('Backend status:', backendAvailable ? 'Available' : 'Not available');
    } catch (error) {
        backendAvailable = false;
        console.log('Backend not available, using static data');
    }
}

// Load products dynamically if backend is available
async function loadProducts() {
    try {
        const response = await fetch(`${API_BASE_URL}/products`);
        if (response.ok) {
            const products = await response.json();
            backendAvailable = true;

            // Update product grid with dynamic data
            updateProductGrid(products);

            // Update sample products for cart functionality
            products.forEach(product => {
                sampleProducts[product.id] = {
                    name: product.name,
                    price: product.price,
                    image: product.image,
                    unit: product.unit || 'item'
                };
            });
        } else {
            throw new Error('Backend not available');
        }
    } catch (error) {
        console.error('Error loading products:', error);
        backendAvailable = false;
        // Use static products if backend is not available
        console.log('Using static product data');
    }
}

// Update product grid with dynamic data
function updateProductGrid(products) {
    const productsGrid = document.querySelector('.products-grid-vertical');
    if (!productsGrid) return;

    productsGrid.innerHTML = '';

    products.forEach(product => {
        const productBox = document.createElement('div');
        productBox.className = 'product-box';
        productBox.setAttribute('data-product-id', product.id);
        productBox.innerHTML = `
            <img src="${product.image}" alt="${product.name}">
            <h3>${product.name}</h3>
            <div class="price">$${product.price}</div>
            <div class="stars">
                ${generateStars(product.rating || 4.5)}
            </div>
            <a href="#" class="btn" data-product-id="${product.id}">add to cart</a>
        `;
        productsGrid.appendChild(productBox);
    });

    // Re-setup add to cart buttons
    setupAddToCartButtons();
}

// Update product slider with dynamic data (for backward compatibility)
function updateProductSlider(products) {
    // Try to update vertical grid first
    updateProductGrid(products);

    // Also update swiper if it exists
    const swiperWrapper = document.querySelector('.swiper-wrapper');
    if (!swiperWrapper) return;

    swiperWrapper.innerHTML = '';

    products.forEach(product => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide box';
        slide.innerHTML = `
            <img src="${product.image}" alt="${product.name}">
            <h3>${product.name}</h3>
            <div class="price">$${product.price}</div>
            <div class="stars">
                ${generateStars(product.rating || 4.5)}
            </div>
            <a href="#" class="btn" data-product-id="${product.id}">add to cart</a>
        `;
        swiperWrapper.appendChild(slide);
    });

    // Reinitialize Swiper
    if (window.swiper) {
        window.swiper.update();
    }
}

// Generate star rating HTML
function generateStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    let starsHTML = '';

    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="fas fa-star"></i>';
    }

    if (hasHalfStar) {
        starsHTML += '<i class="fas fa-star-half-alt"></i>';
    }

    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="far fa-star"></i>';
    }

    return starsHTML;
}

// Shopping cart functionality
let cart = JSON.parse(localStorage.getItem('cart')) || [];

// Enhanced products data for cart functionality
const sampleProducts = {
    1: { name: "Fresh Orange", price: 12.99, image: "image/product-1.png", unit: "kg" },
    2: { name: "Fresh Apple", price: 18.99, image: "image/product-9.jpg", unit: "kg" },
    3: { name: "Fresh Avocado", price: 25.99, image: "image/product-6.png", unit: "kg" },
    4: { name: "Fresh Onion", price: 8.99, image: "image/product-2.png", unit: "kg" },
    5: { name: "Fresh Potato", price: 6.99, image: "image/product-5.png", unit: "kg" },
    6: { name: "Fresh Cabbage", price: 7.99, image: "image/product-4.png", unit: "piece" },
    7: { name: "Basmati Rice", price: 45.99, image: "image/product-7.png", unit: "5kg bag" },
    8: { name: "Whole Wheat Flour", price: 35.99, image: "image/product-10.jpg", unit: "5kg bag" },
    9: { name: "Rolled Oats", price: 28.99, image: "image/product-11.jpg", unit: "1kg pack" },
    10: { name: "Fresh Milk", price: 4.99, image: "image/product-12.jpeg", unit: "1L bottle" },
    11: { name: "Greek Yogurt", price: 8.99, image: "image/product-13.png", unit: "500g container" },
    12: { name: "Olive Oil", price: 24.99, image: "image/product-14.png", unit: "500ml bottle" },
    13: { name: "Sunflower Oil", price: 18.99, image: "image/product-8.png", unit: "1L bottle" },
    14: { name: "Fresh Chicken", price: 32.99, image: "image/product-3.png", unit: "kg" },
    15: { name: "Whole Wheat Bread", price: 5.99, image: "image/cart-img-1.png", unit: "loaf" }
};

function addToCart(productId) {
    // Check if user is logged in
    if (!window.currentUser) {
        showNotification('Please login to add items to cart!', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return;
    }

    // Get product info
    const product = sampleProducts[productId];
    if (!product) {
        showNotification('Product not found!', 'error');
        return;
    }

    // Find existing item in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1,
            addedAt: new Date().toISOString()
        });
    }

    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart display
    updateCartDisplay();

    // Show success message
    showNotification(`${product.name} added to cart!`, 'success');
}

function removeFromCart(productId) {
    // Ensure productId is a number
    productId = parseInt(productId);

    // Find the item to get its name for notification
    const itemToRemove = cart.find(item => item.id === productId);

    // Remove item from cart
    cart = cart.filter(item => item.id !== productId);

    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update display
    updateCartDisplay();

    // Show notification
    const itemName = itemToRemove ? itemToRemove.name : 'Product';
    showNotification(`${itemName} removed from cart!`, 'info');

    // Add animation to cart icon
    animateCartUpdate();
}

function updateCartDisplay() {
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);

    // Update cart icon badge
    const cartBadge = document.getElementById('cart-count');
    if (cartBadge) {
        cartBadge.textContent = cartCount;
        cartBadge.style.display = cartCount > 0 ? 'flex' : 'none';
    }

    // Update cart items display
    updateCartItems();

    // Update cart total
    updateCartTotal();
}

function updateCartItems() {
    const cartItemsContainer = document.getElementById('cart-items');
    if (!cartItemsContainer) return;

    cartItemsContainer.innerHTML = '';

    if (cart.length === 0) {
        cartItemsContainer.innerHTML = '<p class="empty-cart">Your cart is empty</p>';
        return;
    }

    cart.forEach(item => {
        const cartItem = document.createElement('div');
        cartItem.className = 'box';
        const unit = item.unit || 'item';
        cartItem.innerHTML = `
            <i class="fas fa-trash delete-btn" data-product-id="${item.id}" title="Remove from cart"></i>
            <img src="${item.image}" alt="${item.name}">
            <div class="content">
                <h3>${item.name}</h3>
                <span class="price">$${item.price.toFixed(2)} / ${unit}</span>
                <div class="quantity-controls">
                    <button class="qty-btn decrease-btn" data-product-id="${item.id}">-</button>
                    <span class="quantity">qty: ${item.quantity}</span>
                    <button class="qty-btn increase-btn" data-product-id="${item.id}">+</button>
                </div>
            </div>
        `;
        cartItemsContainer.appendChild(cartItem);
    });

    // Add event listeners for delete buttons
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            removeFromCart(productId);
        });
    });

    // Add event listeners for quantity buttons
    document.querySelectorAll('.increase-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            increaseQuantity(productId);
        });
    });

    document.querySelectorAll('.decrease-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const productId = parseInt(this.dataset.productId);
            decreaseQuantity(productId);
        });
    });
}

function updateCartTotal() {
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    const totalElement = document.getElementById('cart-total');
    if (totalElement) {
        totalElement.textContent = `Total: $${total.toFixed(2)}`;
    }

    // Update checkout button
    const checkoutBtn = document.getElementById('checkout-btn');
    if (checkoutBtn) {
        if (cart.length === 0) {
            checkoutBtn.style.display = 'none';
        } else {
            checkoutBtn.style.display = 'block';
            checkoutBtn.href = 'checkout.html';
        }
    }
}

function increaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);
    if (item) {
        item.quantity += 1;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
    }
}

function decreaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);
    if (item && item.quantity > 1) {
        item.quantity -= 1;
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartDisplay();
    } else if (item && item.quantity === 1) {
        removeFromCart(productId);
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
        color: white;
        padding: 1rem 2rem;
        border-radius: 0.5rem;
        z-index: 10000;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Search functionality
function initializeSearch() {
    const searchBox = document.getElementById('search-box');
    if (!searchBox) return;

    searchBox.addEventListener('input', debounce(handleSearch, 300));
}

function handleSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();

    if (searchTerm.length < 2) {
        // Reset to show all products
        if (backendAvailable) {
            loadProducts();
        }
        return;
    }

    if (backendAvailable) {
        searchProductsAPI(searchTerm);
    } else {
        searchProductsLocal(searchTerm);
    }
}

async function searchProductsAPI(searchTerm) {
    try {
        const response = await fetch(`${API_BASE_URL}/products?search=${encodeURIComponent(searchTerm)}`);
        const products = await response.json();
        updateProductGrid(products);
    } catch (error) {
        console.error('Error searching products:', error);
        // Fallback to local search
        searchProductsLocal(searchTerm);
    }
}

function searchProductsLocal(searchTerm) {
    // Search in vertical grid
    const productBoxes = document.querySelectorAll('.products-grid-vertical .product-box, .swiper-slide.box');

    productBoxes.forEach(box => {
        const productName = box.querySelector('h3').textContent.toLowerCase();

        if (productName.includes(searchTerm)) {
            box.style.display = 'block';
        } else {
            box.style.display = 'none';
        }
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    await checkBackend();

    if (backendAvailable) {
        await loadProducts();
    }

    initializeSearch();
    updateCartDisplay();
    setupShopNowButtons();
    setupAddToCartButtons();
    updateNavbarForUser();
    initializeScrollAnimations();
    initializeHeaderEffects();
    initializeNewsletterForm();

    console.log('FreshMart Pro initialized successfully!');
});

// Setup Shop Now buttons
function setupShopNowButtons() {
    // Shop Now button in hero section
    const shopNowBtn = document.querySelector('.home .content .btn');
    if (shopNowBtn) {
        shopNowBtn.addEventListener('click', function(e) {
            e.preventDefault();
            // Scroll to products section
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({ behavior: 'smooth' });
            } else {
                // If not on home page, go to home page products section
                window.location.href = 'index.html#products';
            }
        });
    }

    // Shop Now buttons in categories
    const categoryShopBtns = document.querySelectorAll('.categories .box .btn, .category-box .btn');
    categoryShopBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            // Get category from parent element
            const categoryBox = this.closest('.box');
            const categoryName = categoryBox ? categoryBox.querySelector('h3').textContent.toLowerCase() : 'all';

            // Redirect to categories page with filter
            window.location.href = `categories.html?category=${encodeURIComponent(categoryName)}`;
        });
    });

    // Read More buttons in features
    const readMoreBtns = document.querySelectorAll('.features .box .btn');
    readMoreBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            // Scroll to products or redirect to relevant page
            window.location.href = 'categories.html';
        });
    });
}

// Setup Add to Cart buttons
function setupAddToCartButtons() {
    // Add to cart buttons in product slider
    const addToCartBtns = document.querySelectorAll('.products .btn, .product-box .btn');
    addToCartBtns.forEach((btn, index) => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();

            // Get product ID from button or use index + 1
            let productId = this.dataset.productId || (index + 1);

            // If button is inside a product box, try to get ID from parent
            const productBox = this.closest('.box, .product-box');
            if (productBox) {
                const productImg = productBox.querySelector('img');
                if (productImg && productImg.src.includes('product-')) {
                    // Extract product number from image name
                    const match = productImg.src.match(/product-(\d+)/);
                    if (match) {
                        productId = parseInt(match[1]);
                    }
                }
            }

            addToCart(productId);
        });
    });
}

// Update navbar based on user login status
function updateNavbarForUser() {
    const navbarLogin = document.getElementById('navbar-login');
    const navbarAdmin = document.getElementById('navbar-admin');

    if (window.currentUser) {
        // User is logged in
        if (navbarLogin) {
            navbarLogin.style.display = 'none';
        }

        // Show admin link only for admin users
        if (navbarAdmin && window.currentUser.role === 'admin') {
            navbarAdmin.style.display = 'block';
        }
    } else {
        // User is not logged in
        if (navbarLogin) {
            navbarLogin.style.display = 'block';
        }
        if (navbarAdmin) {
            navbarAdmin.style.display = 'none';
        }
    }
}

// Enhanced add to cart function with better product detection
function addToCartEnhanced(element) {
    // Get product information from the clicked element's context
    const productBox = element.closest('.box, .product-box, .swiper-slide');

    if (!productBox) {
        showNotification('Product information not found!', 'error');
        return;
    }

    // Extract product details
    const productImg = productBox.querySelector('img');
    const productName = productBox.querySelector('h3, h1')?.textContent || 'Unknown Product';
    const productPriceElement = productBox.querySelector('.price');

    let productId = 1;
    let productPrice = 10.99;

    // Try to extract product ID from image
    if (productImg && productImg.src.includes('product-')) {
        const match = productImg.src.match(/product-(\d+)/);
        if (match) {
            productId = parseInt(match[1]);
        }
    }

    // Try to extract price
    if (productPriceElement) {
        const priceText = productPriceElement.textContent;
        const priceMatch = priceText.match(/\$?(\d+\.?\d*)/);
        if (priceMatch) {
            productPrice = parseFloat(priceMatch[1]);
        }
    }

    // Use the existing addToCart function
    addToCart(productId);
}

// Update the existing addToCart function to handle missing products better
const originalAddToCart = window.addToCart || addToCart;
window.addToCart = function(productId) {
    // Check if user is logged in
    if (!window.currentUser) {
        showNotification('Please login to add items to cart!', 'warning');
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 2000);
        return;
    }

    // Ensure productId is a number
    productId = parseInt(productId) || 1;

    // Get product info (use existing sampleProducts or create default)
    let product = sampleProducts[productId];

    if (!product) {
        // Create a default product if not found
        product = {
            name: `Product ${productId}`,
            price: 9.99,
            image: `image/product-${productId}.png`
        };
        sampleProducts[productId] = product;
    }

    // Find existing item in cart
    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            unit: product.unit || 'item',
            quantity: 1,
            addedAt: new Date().toISOString()
        });
    }

    // Save to localStorage
    localStorage.setItem('cart', JSON.stringify(cart));

    // Update cart display
    updateCartDisplay();

    // Show success message
    showNotification(`${product.name} added to cart!`, 'success');
};

// Scroll Animations
function initializeScrollAnimations() {
    const revealElements = document.querySelectorAll('.reveal');

    const revealObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('active');
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    revealElements.forEach(element => {
        revealObserver.observe(element);
    });
}

// Header Effects
function initializeHeaderEffects() {
    const header = document.querySelector('.header');

    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Newsletter Form
function initializeNewsletterForm() {
    const newsletterForm = document.querySelector('.newsletter-form');

    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const email = this.querySelector('input[type="email"]').value;

            if (email) {
                showNotification('Thank you for subscribing to our newsletter!', 'success');
                this.querySelector('input[type="email"]').value = '';

                // Here you would typically send the email to your backend
                if (backendAvailable) {
                    subscribeToNewsletter(email);
                }
            }
        });
    }
}

// Newsletter subscription API call
async function subscribeToNewsletter(email) {
    try {
        const response = await fetch(`${API_BASE_URL}/newsletter/subscribe`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });

        if (response.ok) {
            console.log('Newsletter subscription successful');
        }
    } catch (error) {
        console.error('Newsletter subscription error:', error);
    }
}

// Enhanced product interactions
function addProductHoverEffects() {
    const productBoxes = document.querySelectorAll('.products .box, .product-box');

    productBoxes.forEach(box => {
        box.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.05)';
        });

        box.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    const navLinks = document.querySelectorAll('.navbar a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Enhanced cart functionality with animations
function animateCartUpdate() {
    const cartIcon = document.getElementById('cart-btn');
    const cartBadge = document.getElementById('cart-count');

    if (cartIcon && cartBadge) {
        cartIcon.style.animation = 'none';
        cartBadge.style.animation = 'none';

        setTimeout(() => {
            cartIcon.style.animation = 'bounce 0.6s ease';
            cartBadge.style.animation = 'bounce 0.6s ease';
        }, 10);
    }
}

// Add floating animation to feature images
function addFloatingAnimations() {
    const featureImages = document.querySelectorAll('.features .box img');

    featureImages.forEach((img, index) => {
        img.style.animationDelay = `${index * 0.5}s`;
        img.classList.add('float-animation');
    });
}

// Enhanced search icon functionality
function enhanceSearchIcon() {
    const searchIcon = document.querySelector('.search-icon');
    const searchInput = document.getElementById('search-box');

    if (searchIcon && searchInput) {
        searchIcon.addEventListener('click', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();

            if (searchTerm) {
                // Trigger search
                handleSearch({ target: searchInput });
                showNotification(`Searching for "${searchTerm}"...`, 'info');
            } else {
                // Focus on input if empty
                searchInput.focus();
                showNotification('Please enter a search term', 'warning');
            }
        });
    }
}

// Fix features section display issues
function fixFeaturesSection() {
    const featureBoxes = document.querySelectorAll('.features .box');

    featureBoxes.forEach((box, index) => {
        // Add staggered animation delay
        box.style.animationDelay = `${index * 0.2}s`;

        // Ensure proper display
        box.style.display = 'block';
        box.style.visibility = 'visible';
        box.style.opacity = '1';
    });
}

// Initialize additional features
document.addEventListener('DOMContentLoaded', function() {
    initializeSmoothScrolling();
    addProductHoverEffects();
    enhanceSearchIcon();
    fixFeaturesSection();
    setTimeout(addFloatingAnimations, 1000);
});