// Admin Panel JavaScript

// Get products from localStorage or use default data
function getStoredProducts() {
    const stored = localStorage.getItem('adminProducts');
    if (stored) {
        return JSON.parse(stored);
    }

    // Default products with corrected names
    return [
        {
            id: 1,
            name: "Fresh Orange",
            category: "fruits",
            price: 12.99,
            stock: 50,
            status: "active",
            image: "image/product-1.png",
            unit: "kg",
            description: "Fresh and juicy oranges packed with vitamin C"
        },
        {
            id: 2,
            name: "Fresh Watermelon",
            category: "fruits",
            price: 8.99,
            stock: 40,
            status: "active",
            image: "image/cart-img-1.png",
            unit: "kg",
            description: "Sweet and refreshing watermelon slices"
        },
        {
            id: 3,
            name: "Fresh Avocado",
            category: "fruits",
            price: 25.99,
            stock: 25,
            status: "active",
            image: "image/product-6.png",
            unit: "kg",
            description: "Premium avocados rich in healthy fats"
        },
        {
            id: 4,
            name: "Fresh Onion",
            category: "vegetables",
            price: 8.99,
            stock: 60,
            status: "active",
            image: "image/cart-img-2.png",
            unit: "kg",
            description: "Premium quality onions for your cooking needs"
        },
        {
            id: 5,
            name: "Fresh Potato",
            category: "vegetables",
            price: 6.99,
            stock: 80,
            status: "active",
            image: "image/product-5.png",
            unit: "kg",
            description: "Fresh potatoes perfect for all cooking methods"
        },
        {
            id: 6,
            name: "Fresh Cabbage",
            category: "vegetables",
            price: 7.99,
            stock: 35,
            status: "active",
            image: "image/product-4.png",
            unit: "piece",
            description: "Crisp and fresh cabbage for healthy meals"
        },
        {
            id: 7,
            name: "Basmati Rice",
            category: "grains",
            price: 45.99,
            stock: 100,
            status: "active",
            image: "image/product-7.png",
            unit: "5kg bag",
            description: "Premium quality basmati rice with long grains"
        },
        {
            id: 8,
            name: "Wheat Flour",
            category: "grains",
            price: 35.99,
            stock: 75,
            status: "active",
            image: "image/product-10.jpg",
            unit: "5kg bag",
            description: "100% whole wheat flour for healthy baking"
        },
        {
            id: 9,
            name: "Oats",
            category: "grains",
            price: 28.99,
            stock: 45,
            status: "active",
            image: "image/product-11.jpg",
            unit: "1kg pack",
            description: "Premium rolled oats for healthy breakfast"
        },
        {
            id: 10,
            name: "Fresh Milk",
            category: "dairy",
            price: 4.99,
            stock: 120,
            status: "active",
            image: "image/product-12.jpeg",
            unit: "1L bottle",
            description: "Fresh full-fat milk from local farms"
        },
        {
            id: 11,
            name: "Greek Yogurt",
            category: "dairy",
            price: 8.99,
            stock: 55,
            status: "active",
            image: "image/product-13.png",
            unit: "500g container",
            description: "Creamy Greek yogurt packed with protein"
        },
        {
            id: 12,
            name: "Olive Oil",
            category: "oils",
            price: 24.99,
            stock: 40,
            status: "active",
            image: "image/product-14.png",
            unit: "500ml bottle",
            description: "Extra virgin olive oil for healthy cooking"
        },
        {
            id: 13,
            name: "Sunflower Oil",
            category: "oils",
            price: 18.99,
            stock: 65,
            status: "active",
            image: "image/product-8.png",
            unit: "1L bottle",
            description: "Pure sunflower oil for everyday cooking"
        },
        {
            id: 14,
            name: "Fresh Chicken",
            category: "meat",
            price: 32.99,
            stock: 30,
            status: "active",
            image: "image/cart-img-3.png",
            unit: "kg",
            description: "Fresh chicken from local farms"
        },
        {
            id: 15,
            name: "Fresh Bread",
            category: "bakery",
            price: 5.99,
            stock: 85,
            status: "active",
            image: "image/product-2.png",
            unit: "loaf",
            description: "Fresh bread baked daily"
        }
    ];
}

// Initialize products
let products = getStoredProducts();

// Save products to localStorage and sync with main website
function saveProducts() {
    localStorage.setItem('adminProducts', JSON.stringify(products));

    // Also update the main website's product data
    if (window.sampleProducts) {
        // Update sampleProducts for cart functionality
        products.forEach(product => {
            window.sampleProducts[product.id] = {
                name: product.name,
                price: product.price,
                image: product.image,
                unit: product.unit || 'item'
            };
        });
    }

    // Trigger product grid update if function exists
    if (window.updateProductGrid) {
        window.updateProductGrid(products);
    }

    console.log('Products saved and synced with main website');
}

let orders = [
    {
        id: "ORD001",
        customer: "John Doe",
        date: "2024-01-15",
        total: 45.99,
        status: "delivered"
    },
    {
        id: "ORD002",
        customer: "Jane Smith",
        date: "2024-01-16",
        total: 32.50,
        status: "processing"
    },
    {
        id: "ORD003",
        customer: "Mike Johnson",
        date: "2024-01-17",
        total: 78.25,
        status: "pending"
    }
];

let customers = [
    {
        id: 1,
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+1234567890",
        orders: 5,
        totalSpent: 245.99,
        status: "active"
    },
    {
        id: 2,
        name: "Jane Smith",
        email: "<EMAIL>",
        phone: "+1234567891",
        orders: 3,
        totalSpent: 156.75,
        status: "active"
    }
];

// Initialize admin panel
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for auth.js to load and set currentUser
    setTimeout(() => {
        checkAdminAccess();
    }, 100);

    // Initialize products and sync with main website
    products = getStoredProducts();
    saveProducts(); // This will sync with main website

    updateDashboardStats();
    loadProducts();
    loadOrders();
    loadCustomers();
    initializeEventListeners();

    // Initialize management features if they exist
    if (typeof loadBlogs === 'function') {
        loadBlogs();
    }
    if (typeof loadReviews === 'function') {
        loadReviews();
    }

    console.log('Admin panel initialized with', products.length, 'products');
});

function checkAdminAccess() {
    // Check if user is logged in and is admin
    if (!window.currentUser) {
        showAdminLoginPrompt();
        return;
    }

    if (window.currentUser.role !== 'admin') {
        showNotification('Access denied. Admin privileges required.', 'error');
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
        return;
    }

    // User is admin, show admin content
    showAdminContent();
}

function showAdminLoginPrompt() {
    const adminDashboard = document.querySelector('.admin-dashboard');
    if (adminDashboard) {
        adminDashboard.innerHTML = `
            <div class="admin-login-prompt">
                <div class="login-container">
                    <div class="login-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h2>Admin Access Required</h2>
                    <p>Please login with admin credentials to access the admin panel.</p>
                    <div class="admin-demo-login">
                        <h3>Demo Admin Credentials:</h3>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Password:</strong> admin123</p>
                    </div>
                    <div class="login-actions">
                        <a href="login.html" class="btn">
                            <i class="fas fa-sign-in-alt"></i> Login as Admin
                        </a>
                        <button class="btn secondary" onclick="quickAdminLogin()">
                            <i class="fas fa-bolt"></i> Quick Demo Login
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

function showAdminContent() {
    // Admin is logged in, ensure all content is visible
    const adminDashboard = document.querySelector('.admin-dashboard');
    if (adminDashboard && adminDashboard.querySelector('.admin-login-prompt')) {
        // Reload the page to show admin content
        location.reload();
    }
}

function quickAdminLogin() {
    // Quick login for demo purposes
    const adminUser = {
        id: 2,
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        role: 'admin',
        phone: '+1234567891',
        address: '456 Admin Ave, City, State 12345',
        joinDate: '2024-01-01',
        isActive: true
    };

    window.currentUser = adminUser;
    localStorage.setItem('currentUser', JSON.stringify(adminUser));

    // Update UI
    if (window.updateUserInterface) {
        window.updateUserInterface();
    }

    showNotification('Admin login successful!', 'success');

    // Show admin content
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// Update dashboard statistics
function updateDashboardStats() {
    document.getElementById('total-products').textContent = products.length;
    document.getElementById('total-orders').textContent = orders.length;
    document.getElementById('total-customers').textContent = customers.length;
    
    const totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    document.getElementById('total-revenue').textContent = '$' + totalRevenue.toFixed(2);
}

// Tab switching functionality
function initializeEventListeners() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchTab(tabName);
        });
    });

    // Add product modal
    const addProductBtn = document.getElementById('add-product-btn');
    const modal = document.getElementById('add-product-modal');
    const closeBtn = modal.querySelector('.close');
    const cancelBtn = modal.querySelector('.btn-cancel');

    addProductBtn.addEventListener('click', () => {
        modal.style.display = 'block';
    });

    closeBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    cancelBtn.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Add product form submission
    document.getElementById('add-product-form').addEventListener('submit', handleAddProduct);
}

function switchTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

    // Add active class to selected tab and content
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    document.getElementById(`${tabName}-tab`).classList.add('active');
}

// Load products into table
function loadProducts() {
    const tbody = document.getElementById('products-tbody');
    tbody.innerHTML = '';

    products.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${product.id}</td>
            <td><img src="${product.image}" alt="${product.name}"></td>
            <td>${product.name}</td>
            <td>${product.category}</td>
            <td>$${product.price}</td>
            <td>${product.stock}</td>
            <td><span class="status-badge status-${product.status}">${product.status}</span></td>
            <td>
                <button class="action-btn btn-edit" onclick="editProduct(${product.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn btn-delete" onclick="deleteProduct(${product.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Load orders into table
function loadOrders() {
    const tbody = document.getElementById('orders-tbody');
    tbody.innerHTML = '';

    orders.forEach(order => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${order.id}</td>
            <td>${order.customer}</td>
            <td>${order.date}</td>
            <td>$${order.total}</td>
            <td><span class="status-badge status-${order.status}">${order.status}</span></td>
            <td>
                <button class="action-btn btn-view" onclick="viewOrder('${order.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn btn-edit" onclick="editOrder('${order.id}')">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Load customers into table
function loadCustomers() {
    const tbody = document.getElementById('customers-tbody');
    tbody.innerHTML = '';

    customers.forEach(customer => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${customer.id}</td>
            <td>${customer.name}</td>
            <td>${customer.email}</td>
            <td>${customer.phone}</td>
            <td>${customer.orders}</td>
            <td>$${customer.totalSpent}</td>
            <td><span class="status-badge status-${customer.status}">${customer.status}</span></td>
            <td>
                <button class="action-btn btn-view" onclick="viewCustomer(${customer.id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="action-btn btn-edit" onclick="editCustomer(${customer.id})">
                    <i class="fas fa-edit"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Handle add product form submission
function handleAddProduct(e) {
    e.preventDefault();

    const formData = new FormData(e.target);

    // Generate new ID
    const newId = products.length > 0 ? Math.max(...products.map(p => p.id)) + 1 : 1;

    // Handle image upload (for now, use a default image)
    let imagePath = 'image/default-product.png';
    const imageFile = formData.get('image');
    if (imageFile && imageFile.name) {
        // In a real app, you'd upload the file to server
        // For now, we'll use a placeholder
        imagePath = `image/product-${newId}.png`;
    }

    const newProduct = {
        id: newId,
        name: formData.get('name'),
        category: formData.get('category'),
        price: parseFloat(formData.get('price')),
        stock: parseInt(formData.get('stock')),
        status: 'active',
        image: imagePath,
        unit: getUnitByCategory(formData.get('category')),
        description: formData.get('description') || `Fresh ${formData.get('name')}`,
        rating: 4.0,
        reviews: 0
    };

    products.push(newProduct);

    // Save and sync with main website
    saveProducts();

    // Reload admin interface
    loadProducts();
    updateDashboardStats();

    // Close modal and reset form
    document.getElementById('add-product-modal').style.display = 'none';
    e.target.reset();

    // Show success notification
    if (window.showNotification) {
        window.showNotification(`Product "${newProduct.name}" added successfully!`, 'success');
    } else {
        alert('Product added successfully!');
    }

    console.log('New product added:', newProduct);
}

// Helper function to get unit by category
function getUnitByCategory(category) {
    const unitMap = {
        'fruits': 'kg',
        'vegetables': 'kg',
        'meat': 'kg',
        'dairy': '1L bottle',
        'grains': '1kg pack',
        'oils': '500ml bottle',
        'bakery': 'piece',
        'beverages': '1L bottle'
    };
    return unitMap[category] || 'piece';
}

// Product management functions
function editProduct(id) {
    const product = products.find(p => p.id === id);
    if (product) {
        // Create a simple edit form
        const newName = prompt('Enter new product name:', product.name);
        const newPrice = prompt('Enter new price:', product.price);
        const newStock = prompt('Enter new stock quantity:', product.stock);

        if (newName && newPrice && newStock) {
            product.name = newName;
            product.price = parseFloat(newPrice);
            product.stock = parseInt(newStock);

            // Save and sync
            saveProducts();
            loadProducts();

            if (window.showNotification) {
                window.showNotification(`Product "${product.name}" updated successfully!`, 'success');
            } else {
                alert('Product updated successfully!');
            }
        }
    }
}

function deleteProduct(id) {
    const product = products.find(p => p.id === id);
    if (product && confirm(`Are you sure you want to delete "${product.name}"?`)) {
        products = products.filter(p => p.id !== id);

        // Save and sync
        saveProducts();
        loadProducts();
        updateDashboardStats();

        if (window.showNotification) {
            window.showNotification(`Product "${product.name}" deleted successfully!`, 'info');
        } else {
            alert('Product deleted successfully!');
        }
    }
}

// Order management functions
function viewOrder(id) {
    const order = orders.find(o => o.id === id);
    if (order) {
        alert(`Order Details:\nID: ${order.id}\nCustomer: ${order.customer}\nTotal: $${order.total}\nStatus: ${order.status}`);
    }
}

function editOrder(id) {
    const order = orders.find(o => o.id === id);
    if (order) {
        const newStatus = prompt('Enter new status (pending/processing/shipped/delivered):', order.status);
        if (newStatus && ['pending', 'processing', 'shipped', 'delivered'].includes(newStatus)) {
            order.status = newStatus;
            loadOrders();
            alert('Order status updated successfully!');
        }
    }
}

// Customer management functions
function viewCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        alert(`Customer Details:\nName: ${customer.name}\nEmail: ${customer.email}\nPhone: ${customer.phone}\nTotal Orders: ${customer.orders}\nTotal Spent: $${customer.totalSpent}`);
    }
}

function editCustomer(id) {
    const customer = customers.find(c => c.id === id);
    if (customer) {
        const newEmail = prompt('Enter new email:', customer.email);
        if (newEmail) {
            customer.email = newEmail;
            loadCustomers();
            alert('Customer updated successfully!');
        }
    }
}

// Export functions for global use
window.handleAddProduct = handleAddProduct;
window.editProduct = editProduct;
window.deleteProduct = deleteProduct;
window.viewOrder = viewOrder;
window.editOrder = editOrder;
window.viewCustomer = viewCustomer;
window.editCustomer = editCustomer;
window.quickAdminLogin = quickAdminLogin;
