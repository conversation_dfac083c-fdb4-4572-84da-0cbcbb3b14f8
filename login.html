<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Login - Glossary Store</title>
	
	<!-- Font Awesome CDN -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
	
	<!-- Custom CSS -->
	<link rel="stylesheet" type="text/css" href="css/style.css">
	<link rel="stylesheet" type="text/css" href="css/auth.css">
</head>
<body>

<!-- header section  -->
<header class="header"> 
    <a href="index.html" class="logo"><i class="fas fa-shopping-basket"></i> Glossary Store</a>

<nav class="navbar">
	<a href="index.html">home</a>
	<a href="index.html#features">features</a>
	<a href="index.html#products">products</a>
	<a href="categories.html">categories</a>
	<a href="reviews.html">reviews</a>
	<a href="blogs.html">blogs</a>

	<div class="admin-section">
		<a href="admin.html" class="admin-panel-link" id="navbar-admin" style="display: none;">
			<i class="fas fa-cogs"></i> Admin Panel
		</a>
	</div>
</nav>

<div class="icons">
	<div class="fas fa-bars" id="menu-btn"></div>
	<div class="fas fa-search" id="search-btn" title="Search Products">
		<i class="fas fa-search"></i>
	</div>
	<div class="fas fa-shopping-cart" id="cart-btn" title="Shopping Cart">
		<i class="fas fa-shopping-cart"></i>
		<span class="cart-badge" id="cart-count">0</span>
	</div>
	<div class="fas fa-user" id="login-btn" title="User Account">
		<i class="fas fa-user"></i>
	</div>
</div>

<form class="search-form">
	<input type="search" id="search-box" placeholder="Search products...">
	<label for="search-box" class="fas fa-search"></label>
</form>

<div class="shopping-cart">
	<div id="cart-items">
		<!-- Cart items will be loaded here -->
	</div>
	<div class="Total" id="cart-total">Total: $0.00</div>
	<a href="checkout.html" class="btn" id="checkout-btn">Checkout</a>
</div>

<!-- User Profile Dropdown -->
<div class="user-profile" id="user-profile">
	<div class="profile-info" id="profile-info">
		<div class="avatar">
			<i class="fas fa-user-circle"></i>
		</div>
		<div class="user-details">
			<h4 id="user-name">Guest User</h4>
			<p id="user-email">Please login</p>
		</div>
	</div>
	<div class="profile-menu">
		<a href="profile.html"><i class="fas fa-user"></i> My Profile</a>
		<a href="orders.html"><i class="fas fa-shopping-bag"></i> My Orders</a>
		<a href="wishlist.html"><i class="fas fa-heart"></i> Wishlist</a>
		<a href="addresses.html"><i class="fas fa-map-marker-alt"></i> Addresses</a>
		<a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
	</div>
</div>

</header>
<!-- header section  -->

<!-- login section -->
<section class="auth-section">
	<div class="auth-container">
		<div class="auth-box">
			<div class="auth-header">
				<h2>Welcome Back!</h2>
				<p>Sign in to your account to continue shopping</p>
			</div>

			<form class="auth-form" id="login-form">
				<div class="form-group">
					<label for="email">Email Address</label>
					<input type="email" id="email" name="email" required>
					<i class="fas fa-envelope"></i>
				</div>

				<div class="form-group">
					<label for="password">Password</label>
					<input type="password" id="password" name="password" required>
					<i class="fas fa-lock"></i>
					<span class="password-toggle" onclick="togglePassword()">
						<i class="fas fa-eye" id="password-eye"></i>
					</span>
				</div>

				<div class="form-options">
					<label class="checkbox-container">
						<input type="checkbox" id="remember-me" name="remember-me">
						<span class="checkmark"></span>
						Remember me
					</label>
					<a href="forgot-password.html" class="forgot-link">Forgot Password?</a>
				</div>

				<button type="submit" class="auth-btn">
					<i class="fas fa-sign-in-alt"></i>
					Sign In
				</button>

				<div class="divider">
					<span>or</span>
				</div>

				<div class="social-login">
					<button type="button" class="social-btn google-btn">
						<i class="fab fa-google"></i>
						Continue with Google
					</button>
					<button type="button" class="social-btn facebook-btn">
						<i class="fab fa-facebook-f"></i>
						Continue with Facebook
					</button>
				</div>

				<div class="auth-footer">
					<p>Don't have an account? <a href="register.html">Sign up here</a></p>
				</div>
			</form>
		</div>

		<div class="auth-image">
			<img src="image/banner-img.jpg" alt="Shopping">
			<div class="image-overlay">
				<h3>Fresh & Organic</h3>
				<p>Quality products delivered to your doorstep</p>
			</div>
		</div>
	</div>
</section>

<!-- Demo Login Section -->
<section class="demo-login-section" style="padding: 4rem 2rem; background: #f8f9fa;">
	<div class="container" style="max-width: 800px; margin: 0 auto;">
		<h2 style="text-align: center; margin-bottom: 3rem; color: #333;">
			<i class="fas fa-rocket"></i> Quick Demo Login
		</h2>

		<div class="demo-cards" style="display: grid; grid-template-columns: 1fr 1fr; gap: 3rem;">
			<!-- Customer Login Card -->
			<div class="demo-card customer-card" style="background: #e8f5e9; padding: 2rem; border-radius: 15px; text-align: center; border: 2px solid #4caf50;">
				<div class="demo-icon" style="font-size: 4rem; color: #4caf50; margin-bottom: 1rem;">
					<i class="fas fa-user"></i>
				</div>
				<h3 style="color: #2e7d32; margin-bottom: 1rem;">Customer Login</h3>
				<p style="color: #666; margin-bottom: 1.5rem;">For shopping, browsing products, and placing orders</p>

				<div class="credentials" style="background: white; padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
					<p style="margin: 0.5rem 0;"><strong>Email:</strong> <EMAIL></p>
					<p style="margin: 0.5rem 0;"><strong>Password:</strong> demo123</p>
				</div>

				<button class="btn" onclick="quickLogin('customer')" style="background: #4caf50; padding: 1rem 2rem; font-size: 1.4rem; width: 100%;">
					<i class="fas fa-shopping-cart"></i> Login as Customer
				</button>
			</div>

			<!-- Admin Login Card -->
			<div class="demo-card admin-card" style="background: #ffebee; padding: 2rem; border-radius: 15px; text-align: center; border: 2px solid #f44336;">
				<div class="demo-icon" style="font-size: 4rem; color: #f44336; margin-bottom: 1rem;">
					<i class="fas fa-shield-alt"></i>
				</div>
				<h3 style="color: #c62828; margin-bottom: 1rem;">Admin Login</h3>
				<p style="color: #666; margin-bottom: 1.5rem;">For managing products, orders, and website settings</p>

				<div class="credentials" style="background: white; padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
					<p style="margin: 0.5rem 0;"><strong>Email:</strong> <EMAIL></p>
					<p style="margin: 0.5rem 0;"><strong>Password:</strong> admin123</p>
				</div>

				<div style="background: #ffcdd2; padding: 0.8rem; border-radius: 6px; margin-bottom: 1.5rem;">
					<p style="margin: 0; color: #c62828; font-weight: bold; font-size: 1.2rem;">
						<i class="fas fa-exclamation-triangle"></i> Admin Access Only
					</p>
				</div>

				<button class="btn" onclick="quickLogin('admin')" style="background: #f44336; padding: 1rem 2rem; font-size: 1.4rem; width: 100%;">
					<i class="fas fa-cogs"></i> Login as Admin
				</button>
			</div>
		</div>

		<div style="text-align: center; margin-top: 2rem; color: #666;">
			<p><i class="fas fa-info-circle"></i> These are demo accounts for testing purposes</p>
		</div>
	</div>
</section>

<script src="js/script.js"></script>
<script src="js/auth.js"></script>

</body>
</html>
