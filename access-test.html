<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Access Control Test - Glossary Store</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        
        .user-card {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        .admin-card {
            border-left-color: #f44336;
        }
        .guest-card {
            border-left-color: #ff9800;
        }
        
        .access-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .access-item {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .access-allowed {
            border: 2px solid #4CAF50;
            background: #e8f5e9;
        }
        
        .access-denied {
            border: 2px solid #f44336;
            background: #ffebee;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Access Control Test</h1>
        
        <div class="test-section info">
            <h3>📊 Current Status</h3>
            <div class="user-card" id="current-user-card">
                <p><strong>Current User:</strong> <span id="current-user-name">Not logged in</span></p>
                <p><strong>Role:</strong> <span id="current-user-role">Guest</span></p>
                <p><strong>Admin Access:</strong> <span id="admin-access-status">❌ Denied</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>👤 Login Test Scenarios</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                <button onclick="testGuestAccess()" style="background: #ff9800;">
                    <i class="fas fa-user-slash"></i> Test as Guest
                </button>
                <button onclick="testCustomerAccess()" style="background: #4CAF50;">
                    <i class="fas fa-user"></i> Test as Customer
                </button>
                <button onclick="testAdminAccess()" style="background: #f44336;">
                    <i class="fas fa-shield-alt"></i> Test as Admin
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>🔗 Access Test Results</h3>
            <div class="access-grid" id="access-results">
                <div class="access-item">
                    <h4><i class="fas fa-home"></i> Home Page</h4>
                    <p>Public access</p>
                    <span class="access-status" id="home-access">Testing...</span>
                </div>
                <div class="access-item">
                    <h4><i class="fas fa-shopping-cart"></i> Shopping Cart</h4>
                    <p>Requires login</p>
                    <span class="access-status" id="cart-access">Testing...</span>
                </div>
                <div class="access-item">
                    <h4><i class="fas fa-cogs"></i> Admin Panel</h4>
                    <p>Admin only</p>
                    <span class="access-status" id="admin-panel-access">Testing...</span>
                </div>
                <div class="access-item">
                    <h4><i class="fas fa-plus"></i> Add Products</h4>
                    <p>Admin only</p>
                    <span class="access-status" id="add-product-access">Testing...</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Navigation Test</h3>
            <p>Check if admin links are properly shown/hidden:</p>
            <div style="background: #f9f9f9; padding: 15px; border-radius: 8px;">
                <p><strong>Admin Nav Link Visible:</strong> <span id="nav-link-status">Checking...</span></p>
                <p><strong>Admin Panel Access:</strong> <span id="panel-access-status">Checking...</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results Log</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Test functions
        function testGuestAccess() {
            addTestResult('Testing guest access...', 'info');
            
            // Clear user data
            localStorage.removeItem('currentUser');
            window.currentUser = null;
            
            updateCurrentUserDisplay();
            testAllAccess();
            
            addTestResult('✅ Guest access test completed', 'success');
        }

        function testCustomerAccess() {
            addTestResult('Testing customer access...', 'info');
            
            const customerUser = {
                id: 1,
                firstName: 'Demo',
                lastName: 'Customer',
                email: '<EMAIL>',
                role: 'customer',
                isActive: true
            };

            localStorage.setItem('currentUser', JSON.stringify(customerUser));
            window.currentUser = customerUser;
            
            updateCurrentUserDisplay();
            testAllAccess();
            
            addTestResult('✅ Customer access test completed', 'success');
        }

        function testAdminAccess() {
            addTestResult('Testing admin access...', 'info');
            
            const adminUser = {
                id: 2,
                firstName: 'Admin',
                lastName: 'User',
                email: '<EMAIL>',
                role: 'admin',
                isActive: true
            };

            localStorage.setItem('currentUser', JSON.stringify(adminUser));
            window.currentUser = adminUser;
            
            updateCurrentUserDisplay();
            testAllAccess();
            
            addTestResult('✅ Admin access test completed', 'success');
        }

        function updateCurrentUserDisplay() {
            const currentUser = window.currentUser;
            
            if (currentUser) {
                document.getElementById('current-user-name').textContent = `${currentUser.firstName} ${currentUser.lastName}`;
                document.getElementById('current-user-role').textContent = currentUser.role;
                
                if (currentUser.role === 'admin') {
                    document.getElementById('admin-access-status').textContent = '✅ Allowed';
                    document.getElementById('current-user-card').className = 'user-card admin-card';
                } else {
                    document.getElementById('admin-access-status').textContent = '❌ Denied';
                    document.getElementById('current-user-card').className = 'user-card';
                }
            } else {
                document.getElementById('current-user-name').textContent = 'Not logged in';
                document.getElementById('current-user-role').textContent = 'Guest';
                document.getElementById('admin-access-status').textContent = '❌ Denied';
                document.getElementById('current-user-card').className = 'user-card guest-card';
            }
        }

        function testAllAccess() {
            const currentUser = window.currentUser;
            
            // Test home page access (always allowed)
            document.getElementById('home-access').textContent = '✅ Allowed';
            document.getElementById('home-access').parentElement.className = 'access-item access-allowed';
            
            // Test cart access (requires login)
            if (currentUser) {
                document.getElementById('cart-access').textContent = '✅ Allowed';
                document.getElementById('cart-access').parentElement.className = 'access-item access-allowed';
            } else {
                document.getElementById('cart-access').textContent = '❌ Login Required';
                document.getElementById('cart-access').parentElement.className = 'access-item access-denied';
            }
            
            // Test admin panel access (admin only)
            if (currentUser && currentUser.role === 'admin') {
                document.getElementById('admin-panel-access').textContent = '✅ Allowed';
                document.getElementById('admin-panel-access').parentElement.className = 'access-item access-allowed';
                
                document.getElementById('add-product-access').textContent = '✅ Allowed';
                document.getElementById('add-product-access').parentElement.className = 'access-item access-allowed';
            } else {
                document.getElementById('admin-panel-access').textContent = '❌ Admin Only';
                document.getElementById('admin-panel-access').parentElement.className = 'access-item access-denied';
                
                document.getElementById('add-product-access').textContent = '❌ Admin Only';
                document.getElementById('add-product-access').parentElement.className = 'access-item access-denied';
            }
            
            // Test navigation visibility
            testNavigationVisibility();
        }

        function testNavigationVisibility() {
            const currentUser = window.currentUser;
            
            // Simulate admin link visibility check
            if (currentUser && currentUser.role === 'admin') {
                document.getElementById('nav-link-status').textContent = '✅ Visible';
                document.getElementById('panel-access-status').textContent = '✅ Full Access';
            } else {
                document.getElementById('nav-link-status').textContent = '❌ Hidden';
                document.getElementById('panel-access-status').textContent = '❌ Restricted';
            }
        }

        function addTestResult(message, type) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // Initialize
        window.onload = function() {
            addTestResult('🚀 Access control test page loaded', 'success');
            updateCurrentUserDisplay();
            testAllAccess();
        };
    </script>
</body>
</html>
