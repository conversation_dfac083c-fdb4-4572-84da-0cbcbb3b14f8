<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Test - Product Management</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        
        .product-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .product-card {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            background: #f9f9f9;
        }
        .product-card img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 5px;
        }
        .product-card h4 {
            margin: 10px 0 5px 0;
            color: #333;
        }
        .product-card p {
            margin: 5px 0;
            color: #666;
        }
        .add-product-form {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Admin Product Management Test</h1>
        
        <div class="test-section info">
            <h3>📊 Current Status</h3>
            <p><strong>Admin Status:</strong> <span id="admin-status">Checking...</span></p>
            <p><strong>Products in Admin:</strong> <span id="admin-products-count">0</span></p>
            <p><strong>Products in Main Site:</strong> <span id="main-products-count">0</span></p>
            <p><strong>Sync Status:</strong> <span id="sync-status">Checking...</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Test Actions</h3>
            <button onclick="loginAsAdmin()">Login as Admin</button>
            <button onclick="loadAdminProducts()">Load Admin Products</button>
            <button onclick="syncProducts()">Sync Products</button>
            <button onclick="checkMainSite()">Check Main Site</button>
            <button onclick="clearAllData()">Clear All Data</button>
        </div>

        <div class="test-section">
            <h3>➕ Add New Product</h3>
            <div class="add-product-form">
                <form id="test-add-product-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="test-name">Product Name:</label>
                            <input type="text" id="test-name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="test-category">Category:</label>
                            <select id="test-category" name="category" required>
                                <option value="">Select Category</option>
                                <option value="fruits">Fruits</option>
                                <option value="vegetables">Vegetables</option>
                                <option value="dairy">Dairy</option>
                                <option value="meat">Meat</option>
                                <option value="grains">Grains</option>
                                <option value="oils">Oils</option>
                                <option value="bakery">Bakery</option>
                                <option value="beverages">Beverages</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="test-price">Price ($):</label>
                            <input type="number" id="test-price" name="price" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="test-stock">Stock Quantity:</label>
                            <input type="number" id="test-stock" name="stock" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="test-description">Description:</label>
                        <textarea id="test-description" name="description" rows="3"></textarea>
                    </div>
                    <button type="submit">Add Product</button>
                </form>
            </div>
        </div>

        <div class="test-section">
            <h3>📦 Current Products</h3>
            <div id="products-display" class="product-grid">
                <p>Click "Load Admin Products" to see current products</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Test functions
        function loginAsAdmin() {
            addTestResult('Logging in as admin...', 'info');
            
            const adminUser = {
                id: 2,
                firstName: 'Admin',
                lastName: 'User',
                email: '<EMAIL>',
                role: 'admin',
                isActive: true
            };

            window.currentUser = adminUser;
            localStorage.setItem('currentUser', JSON.stringify(adminUser));
            
            document.getElementById('admin-status').textContent = '✅ Logged in as Admin';
            addTestResult('✅ Admin login successful', 'success');
        }

        function loadAdminProducts() {
            addTestResult('Loading admin products...', 'info');
            
            if (typeof getStoredProducts === 'function') {
                const adminProducts = getStoredProducts();
                document.getElementById('admin-products-count').textContent = adminProducts.length;
                displayProducts(adminProducts);
                addTestResult(`✅ Loaded ${adminProducts.length} products from admin`, 'success');
            } else {
                addTestResult('❌ Admin functions not available', 'error');
            }
        }

        function syncProducts() {
            addTestResult('Syncing products...', 'info');
            
            if (typeof saveProducts === 'function') {
                saveProducts();
                addTestResult('✅ Products synced successfully', 'success');
                checkMainSite();
            } else {
                addTestResult('❌ Sync function not available', 'error');
            }
        }

        function checkMainSite() {
            addTestResult('Checking main site products...', 'info');
            
            if (window.sampleProducts) {
                const mainCount = Object.keys(window.sampleProducts).length;
                document.getElementById('main-products-count').textContent = mainCount;
                document.getElementById('sync-status').textContent = '✅ Synced';
                addTestResult(`✅ Main site has ${mainCount} products`, 'success');
            } else {
                document.getElementById('sync-status').textContent = '❌ Not synced';
                addTestResult('❌ Main site products not found', 'error');
            }
        }

        function clearAllData() {
            if (confirm('Are you sure you want to clear all data?')) {
                localStorage.removeItem('adminProducts');
                localStorage.removeItem('currentUser');
                addTestResult('🗑️ All data cleared', 'warning');
                location.reload();
            }
        }

        function displayProducts(products) {
            const container = document.getElementById('products-display');
            container.innerHTML = '';

            products.forEach(product => {
                const productDiv = document.createElement('div');
                productDiv.className = 'product-card';
                productDiv.innerHTML = `
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNDAiIHk9IjQwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5ObyBJbWFnZTwvdGV4dD48L3N2Zz4='">
                    <h4>${product.name}</h4>
                    <p><strong>$${product.price}</strong> / ${product.unit || 'item'}</p>
                    <p>Category: ${product.category}</p>
                    <p>Stock: ${product.stock}</p>
                    <p>Status: ${product.status}</p>
                `;
                container.appendChild(productDiv);
            });
        }

        // Handle test add product form
        document.getElementById('test-add-product-form').addEventListener('submit', function(e) {
            e.preventDefault();
            addTestResult('Testing add product...', 'info');
            
            if (typeof handleAddProduct === 'function') {
                handleAddProduct(e);
                addTestResult('✅ Product add function called', 'success');
                setTimeout(() => {
                    loadAdminProducts();
                    checkMainSite();
                }, 500);
            } else {
                addTestResult('❌ Add product function not available', 'error');
            }
        });

        function addTestResult(message, type) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // Initialize
        window.onload = function() {
            addTestResult('🚀 Admin test page loaded', 'success');
            setTimeout(() => {
                loadAdminProducts();
                checkMainSite();
            }, 500);
        };
    </script>
</body>
</html>
