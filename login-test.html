<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login Test - Glossary Store</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background: #f1f8e9; }
        .error { border-color: #f44336; background: #ffebee; }
        .info { border-color: #2196F3; background: #e3f2fd; }
        .warning { border-color: #ff9800; background: #fff3e0; }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #45a049; }
        
        .login-form {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        
        .user-status {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .demo-credentials {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Login System Test</h1>
        
        <div class="test-section info">
            <h3>📊 Current Status</h3>
            <div class="user-status">
                <p><strong>Login Status:</strong> <span id="login-status">Checking...</span></p>
                <p><strong>Current User:</strong> <span id="current-user">None</span></p>
                <p><strong>User Role:</strong> <span id="user-role">Guest</span></p>
                <p><strong>Local Storage:</strong> <span id="storage-status">Checking...</span></p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Test Buttons</h3>
            <button onclick="testCustomerLogin()">Test Customer Login</button>
            <button onclick="testAdminLogin()">Test Admin Login</button>
            <button onclick="testLogout()">Test Logout</button>
            <button onclick="checkLoginStatus()">Check Status</button>
            <button onclick="clearStorage()">Clear Storage</button>
        </div>

        <div class="test-section">
            <h3>📝 Manual Login Form</h3>
            <div class="login-form">
                <form id="test-login-form">
                    <div class="form-group">
                        <label for="test-email">Email:</label>
                        <input type="email" id="test-email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="test-password">Password:</label>
                        <input type="password" id="test-password" name="password" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="remember-me"> Remember me
                        </label>
                    </div>
                    <button type="submit">Login</button>
                </form>
            </div>
        </div>

        <div class="test-section">
            <h3>👥 Demo Credentials</h3>
            <div class="demo-credentials">
                <h4>Customer Account:</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> demo123</p>
                <button onclick="fillCredentials('customer')">Fill Customer Credentials</button>
                
                <h4>Admin Account:</h4>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> admin123</p>
                <button onclick="fillCredentials('admin')">Fill Admin Credentials</button>
            </div>
        </div>

        <div class="test-section">
            <h3>🛒 Cart Test (Login Required)</h3>
            <button onclick="testAddToCart()">Test Add to Cart</button>
            <div id="cart-test-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Test Results</h3>
            <div id="test-results"></div>
        </div>
    </div>

    <script src="js/script.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Test functions
        function testCustomerLogin() {
            addTestResult('Testing customer login...', 'info');
            quickLogin('customer');
            setTimeout(checkLoginStatus, 500);
        }

        function testAdminLogin() {
            addTestResult('Testing admin login...', 'info');
            quickLogin('admin');
            setTimeout(checkLoginStatus, 500);
        }

        function testLogout() {
            addTestResult('Testing logout...', 'info');
            handleLogout();
            setTimeout(checkLoginStatus, 500);
        }

        function checkLoginStatus() {
            const currentUser = window.currentUser || JSON.parse(localStorage.getItem('currentUser'));
            
            document.getElementById('login-status').textContent = currentUser ? '✅ Logged In' : '❌ Not Logged In';
            document.getElementById('current-user').textContent = currentUser ? `${currentUser.firstName} ${currentUser.lastName}` : 'None';
            document.getElementById('user-role').textContent = currentUser ? currentUser.role : 'Guest';
            document.getElementById('storage-status').textContent = localStorage.getItem('currentUser') ? '✅ User in Storage' : '❌ No User in Storage';
            
            addTestResult(`Status check: ${currentUser ? 'Logged in as ' + currentUser.firstName : 'Not logged in'}`, currentUser ? 'success' : 'warning');
        }

        function clearStorage() {
            localStorage.removeItem('currentUser');
            localStorage.removeItem('rememberUser');
            window.currentUser = null;
            addTestResult('Storage cleared', 'info');
            checkLoginStatus();
        }

        function fillCredentials(type) {
            if (type === 'customer') {
                document.getElementById('test-email').value = '<EMAIL>';
                document.getElementById('test-password').value = 'demo123';
            } else {
                document.getElementById('test-email').value = '<EMAIL>';
                document.getElementById('test-password').value = 'admin123';
            }
            addTestResult(`${type} credentials filled`, 'info');
        }

        function testAddToCart() {
            const result = document.getElementById('cart-test-result');
            if (window.currentUser) {
                result.innerHTML = '<div class="success">✅ User is logged in - Cart should work</div>';
                addTestResult('Cart test: User logged in, cart should work', 'success');
            } else {
                result.innerHTML = '<div class="error">❌ User not logged in - Cart will show login message</div>';
                addTestResult('Cart test: User not logged in, cart will require login', 'warning');
            }
        }

        // Handle manual login form
        document.getElementById('test-login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            addTestResult('Testing manual login form...', 'info');
            
            const formData = new FormData(e.target);
            const email = formData.get('email');
            const password = formData.get('password');
            
            // Use the same login logic as auth.js
            const users = JSON.parse(localStorage.getItem('users')) || [
                {
                    id: 1,
                    firstName: 'Demo',
                    lastName: 'Customer',
                    email: '<EMAIL>',
                    password: 'demo123',
                    role: 'customer',
                    isActive: true
                },
                {
                    id: 2,
                    firstName: 'Admin',
                    lastName: 'User',
                    email: '<EMAIL>',
                    password: 'admin123',
                    role: 'admin',
                    isActive: true
                }
            ];
            
            const user = users.find(u => u.email === email && u.password === password);
            
            if (user) {
                const currentUser = { ...user };
                delete currentUser.password;
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                window.currentUser = currentUser;
                addTestResult(`✅ Manual login successful for ${currentUser.firstName}`, 'success');
                checkLoginStatus();
            } else {
                addTestResult('❌ Manual login failed - Invalid credentials', 'error');
            }
        });

        function addTestResult(message, type) {
            const container = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
            container.appendChild(resultDiv);
            container.scrollTop = container.scrollHeight;
        }

        // Initialize
        window.onload = function() {
            addTestResult('🚀 Login test page loaded', 'success');
            checkLoginStatus();
        };
    </script>
</body>
</html>
