const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files
app.use(express.static(path.join(__dirname)));

// JSON Database files
const DATA_DIR = path.join(__dirname, 'data');
const PRODUCTS_FILE = path.join(DATA_DIR, 'products.json');
const ORDERS_FILE = path.join(DATA_DIR, 'orders.json');
const CUSTOMERS_FILE = path.join(DATA_DIR, 'customers.json');
const REVIEWS_FILE = path.join(DATA_DIR, 'reviews.json');
const BLOGS_FILE = path.join(DATA_DIR, 'blogs.json');

// Create data directory if it doesn't exist
if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR);
}

// Initialize JSON files with sample data
function initializeData() {
    // Products data
    const productsData = [
        // Fruits
        {
            id: 1,
            name: "Fresh Orange",
            category: "fruits",
            price: 12.99,
            originalPrice: 15.99,
            stock: 50,
            status: "active",
            image: "image/product-1.png",
            description: "Fresh and juicy oranges packed with vitamin C",
            rating: 4.5,
            reviews: 25,
            unit: "kg"
        },
        {
            id: 2,
            name: "Fresh Watermelon",
            category: "fruits",
            price: 8.99,
            originalPrice: 12.99,
            stock: 40,
            status: "active",
            image: "image/cart-img-1.png",
            description: "Sweet and refreshing watermelon slices",
            rating: 4.7,
            reviews: 32,
            unit: "kg"
        },
        {
            id: 3,
            name: "Fresh Avocado",
            category: "fruits",
            price: 25.99,
            originalPrice: 28.99,
            stock: 25,
            status: "active",
            image: "image/product-6.png",
            description: "Premium avocados rich in healthy fats",
            rating: 4.6,
            reviews: 28,
            unit: "kg"
        },

        // Vegetables
        {
            id: 4,
            name: "Fresh Onion",
            category: "vegetables",
            price: 8.99,
            originalPrice: 12.99,
            stock: 60,
            status: "active",
            image: "image/cart-img-2.png",
            description: "Premium quality onions for your cooking needs",
            rating: 4.2,
            reviews: 18,
            unit: "kg"
        },
        {
            id: 5,
            name: "Fresh Potato",
            category: "vegetables",
            price: 6.99,
            originalPrice: 9.99,
            stock: 80,
            status: "active",
            image: "image/product-5.png",
            description: "Fresh potatoes perfect for all cooking methods",
            rating: 4.3,
            reviews: 45,
            unit: "kg"
        },
        {
            id: 6,
            name: "Fresh Cabbage",
            category: "vegetables",
            price: 7.99,
            originalPrice: 10.99,
            stock: 35,
            status: "active",
            image: "image/product-4.png",
            description: "Crisp and fresh cabbage for healthy meals",
            rating: 4.1,
            reviews: 22,
            unit: "piece"
        },

        // Grains & Cereals
        {
            id: 7,
            name: "Basmati Rice",
            category: "grains",
            price: 45.99,
            originalPrice: 52.99,
            stock: 100,
            status: "active",
            image: "image/product-7.png",
            description: "Premium quality basmati rice with long grains",
            rating: 4.8,
            reviews: 67,
            unit: "5kg bag"
        },
        {
            id: 8,
            name: "Wheat Flour",
            category: "grains",
            price: 35.99,
            originalPrice: 42.99,
            stock: 75,
            status: "active",
            image: "image/product-10.jpg",
            description: "100% whole wheat flour for healthy baking",
            rating: 4.5,
            reviews: 54,
            unit: "5kg bag"
        },
        {
            id: 9,
            name: "Oats",
            category: "grains",
            price: 28.99,
            originalPrice: 34.99,
            stock: 45,
            status: "active",
            image: "image/product-11.jpg",
            description: "Premium rolled oats for healthy breakfast",
            rating: 4.6,
            reviews: 38,
            unit: "1kg pack"
        },

        // Dairy Products
        {
            id: 10,
            name: "Fresh Milk",
            category: "dairy",
            price: 4.99,
            originalPrice: 6.99,
            stock: 120,
            status: "active",
            image: "image/product-12.jpeg",
            description: "Fresh full-fat milk from local farms",
            rating: 4.4,
            reviews: 89,
            unit: "1L bottle"
        },
        {
            id: 11,
            name: "Greek Yogurt",
            category: "dairy",
            price: 8.99,
            originalPrice: 11.99,
            stock: 55,
            status: "active",
            image: "image/product-13.png",
            description: "Creamy Greek yogurt packed with protein",
            rating: 4.7,
            reviews: 43,
            unit: "500g container"
        },

        // Cooking Oils & Essentials
        {
            id: 12,
            name: "Olive Oil",
            category: "oils",
            price: 24.99,
            originalPrice: 29.99,
            stock: 40,
            status: "active",
            image: "image/product-14.png",
            description: "Extra virgin olive oil for healthy cooking",
            rating: 4.8,
            reviews: 76,
            unit: "500ml bottle"
        },
        {
            id: 13,
            name: "Sunflower Oil",
            category: "oils",
            price: 18.99,
            originalPrice: 23.99,
            stock: 65,
            status: "active",
            image: "image/product-8.png",
            description: "Pure sunflower oil for everyday cooking",
            rating: 4.3,
            reviews: 52,
            unit: "1L bottle"
        },

        // Meat & Poultry
        {
            id: 14,
            name: "Fresh Chicken",
            category: "meat",
            price: 32.99,
            originalPrice: 38.99,
            stock: 30,
            status: "active",
            image: "image/cart-img-3.png",
            description: "Fresh chicken from local farms",
            rating: 4.5,
            reviews: 34,
            unit: "kg"
        },

        // Bakery
        {
            id: 15,
            name: "Fresh Bread",
            category: "bakery",
            price: 5.99,
            originalPrice: 7.99,
            stock: 85,
            status: "active",
            image: "image/product-2.png",
            description: "Fresh bread baked daily",
            rating: 4.4,
            reviews: 67,
            unit: "loaf"
        }
        {
            id: 3,
            name: "Fresh Meat",
            category: "meat",
            price: 25.99,
            originalPrice: 30.99,
            stock: 20,
            status: "active",
            image: "image/product-3.png",
            description: "High-quality fresh meat from trusted sources",
            rating: 4.8,
            reviews: 42
        },
        {
            id: 4,
            name: "Fresh Cabbage",
            category: "vegetables",
            price: 8.99,
            originalPrice: 10.99,
            stock: 40,
            status: "active",
            image: "image/product-4.png",
            description: "Crisp and fresh cabbage perfect for salads",
            rating: 4.0,
            reviews: 12
        },
        {
            id: 5,
            name: "Fresh Potato",
            category: "vegetables",
            price: 5.99,
            originalPrice: 7.99,
            stock: 60,
            status: "active",
            image: "image/product-5.png",
            description: "Farm-fresh potatoes ideal for all cooking methods",
            rating: 4.3,
            reviews: 35
        },
        {
            id: 6,
            name: "Fresh Avocado",
            category: "fruits",
            price: 18.99,
            originalPrice: 22.99,
            stock: 25,
            status: "active",
            image: "image/product-6.png",
            description: "Creamy and nutritious avocados rich in healthy fats",
            rating: 4.7,
            reviews: 28
        }
    ];

    // Orders data
    const ordersData = [
        {
            id: "ORD001",
            customer: "John Doe",
            email: "<EMAIL>",
            phone: "+**********",
            address: "123 Main St, City, State 12345",
            date: "2024-01-15",
            items: [
                { productId: 1, name: "Fresh Orange", quantity: 2, price: 12.99 },
                { productId: 3, name: "Fresh Meat", quantity: 1, price: 25.99 }
            ],
            total: 51.97,
            status: "delivered"
        },
        {
            id: "ORD002",
            customer: "Jane Smith",
            email: "<EMAIL>",
            phone: "+**********",
            address: "456 Oak Ave, City, State 12345",
            date: "2024-01-16",
            items: [
                { productId: 2, name: "Fresh Onion", quantity: 1, price: 15.99 },
                { productId: 4, name: "Fresh Cabbage", quantity: 2, price: 8.99 }
            ],
            total: 33.97,
            status: "processing"
        }
    ];

    // Customers data
    const customersData = [
        {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
            phone: "+**********",
            address: "123 Main St, City, State 12345",
            orders: 5,
            totalSpent: 245.99,
            status: "active",
            joinDate: "2023-12-01"
        },
        {
            id: 2,
            name: "Jane Smith",
            email: "<EMAIL>",
            phone: "+**********",
            address: "456 Oak Ave, City, State 12345",
            orders: 3,
            totalSpent: 156.75,
            status: "active",
            joinDate: "2024-01-05"
        }
    ];

    // Reviews data
    const reviewsData = [
        {
            id: 1,
            productId: 1,
            customerName: "Sarah Johnson",
            email: "<EMAIL>",
            rating: 5,
            comment: "Amazing quality products! Fresh vegetables and fruits delivered right to my doorstep.",
            date: "2024-01-10",
            verified: true
        },
        {
            id: 2,
            productId: 3,
            customerName: "Mike Chen",
            email: "<EMAIL>",
            rating: 4,
            comment: "Great service and competitive prices. The organic fruits are really fresh and tasty.",
            date: "2024-01-12",
            verified: true
        }
    ];

    // Blogs data
    const blogsData = [
        {
            id: 1,
            title: "Benefits of Organic Food",
            slug: "benefits-of-organic-food",
            author: "Admin",
            date: "2024-01-01",
            image: "image/blog-1.jpg",
            excerpt: "Discover the amazing health benefits of choosing organic foods for you and your family.",
            content: "Organic food has become increasingly popular as people become more health-conscious...",
            category: "Health",
            tags: ["organic", "health", "nutrition"],
            published: true
        },
        {
            id: 2,
            title: "Seasonal Vegetables Guide",
            slug: "seasonal-vegetables-guide",
            author: "Nutritionist",
            date: "2024-01-05",
            image: "image/blog-2.jpg",
            excerpt: "A comprehensive guide to seasonal vegetables and how to incorporate them into your daily meals.",
            content: "Eating seasonal vegetables is not only better for your health but also for the environment...",
            category: "Nutrition",
            tags: ["vegetables", "seasonal", "cooking"],
            published: true
        }
    ];

    // Write data to files if they don't exist
    if (!fs.existsSync(PRODUCTS_FILE)) {
        fs.writeFileSync(PRODUCTS_FILE, JSON.stringify(productsData, null, 2));
    }
    if (!fs.existsSync(ORDERS_FILE)) {
        fs.writeFileSync(ORDERS_FILE, JSON.stringify(ordersData, null, 2));
    }
    if (!fs.existsSync(CUSTOMERS_FILE)) {
        fs.writeFileSync(CUSTOMERS_FILE, JSON.stringify(customersData, null, 2));
    }
    if (!fs.existsSync(REVIEWS_FILE)) {
        fs.writeFileSync(REVIEWS_FILE, JSON.stringify(reviewsData, null, 2));
    }
    if (!fs.existsSync(BLOGS_FILE)) {
        fs.writeFileSync(BLOGS_FILE, JSON.stringify(blogsData, null, 2));
    }
}

// Helper functions to read/write JSON files
function readJSONFile(filePath) {
    try {
        const data = fs.readFileSync(filePath, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.error(`Error reading ${filePath}:`, error);
        return [];
    }
}

function writeJSONFile(filePath, data) {
    try {
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
        return true;
    } catch (error) {
        console.error(`Error writing ${filePath}:`, error);
        return false;
    }
}

// Initialize data on server start
initializeData();

// Routes

// Serve main pages
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/categories', (req, res) => {
    res.sendFile(path.join(__dirname, 'categories.html'));
});

app.get('/reviews', (req, res) => {
    res.sendFile(path.join(__dirname, 'reviews.html'));
});

app.get('/blogs', (req, res) => {
    res.sendFile(path.join(__dirname, 'blogs.html'));
});

app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'admin.html'));
});

// API Routes

// Products API
app.get('/api/products', (req, res) => {
    const products = readJSONFile(PRODUCTS_FILE);
    const { category, search, limit } = req.query;
    
    let filteredProducts = products;
    
    if (category && category !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.category === category);
    }
    
    if (search) {
        filteredProducts = filteredProducts.filter(p => 
            p.name.toLowerCase().includes(search.toLowerCase()) ||
            p.description.toLowerCase().includes(search.toLowerCase())
        );
    }
    
    if (limit) {
        filteredProducts = filteredProducts.slice(0, parseInt(limit));
    }
    
    res.json(filteredProducts);
});

app.get('/api/products/:id', (req, res) => {
    const products = readJSONFile(PRODUCTS_FILE);
    const product = products.find(p => p.id === parseInt(req.params.id));
    
    if (product) {
        res.json(product);
    } else {
        res.status(404).json({ error: 'Product not found' });
    }
});

app.post('/api/products', (req, res) => {
    const products = readJSONFile(PRODUCTS_FILE);
    const newProduct = {
        id: Math.max(...products.map(p => p.id)) + 1,
        ...req.body,
        status: 'active'
    };
    
    products.push(newProduct);
    
    if (writeJSONFile(PRODUCTS_FILE, products)) {
        res.status(201).json(newProduct);
    } else {
        res.status(500).json({ error: 'Failed to save product' });
    }
});

// Orders API
app.get('/api/orders', (req, res) => {
    const orders = readJSONFile(ORDERS_FILE);
    res.json(orders);
});

app.post('/api/orders', (req, res) => {
    const orders = readJSONFile(ORDERS_FILE);
    const newOrder = {
        id: `ORD${String(orders.length + 1).padStart(3, '0')}`,
        ...req.body,
        date: new Date().toISOString().split('T')[0],
        status: 'pending'
    };
    
    orders.push(newOrder);
    
    if (writeJSONFile(ORDERS_FILE, orders)) {
        res.status(201).json(newOrder);
    } else {
        res.status(500).json({ error: 'Failed to save order' });
    }
});

// Reviews API
app.get('/api/reviews', (req, res) => {
    const reviews = readJSONFile(REVIEWS_FILE);
    const { productId } = req.query;
    
    let filteredReviews = reviews;
    if (productId) {
        filteredReviews = reviews.filter(r => r.productId === parseInt(productId));
    }
    
    res.json(filteredReviews);
});

app.post('/api/reviews', (req, res) => {
    const reviews = readJSONFile(REVIEWS_FILE);
    const newReview = {
        id: Math.max(...reviews.map(r => r.id)) + 1,
        ...req.body,
        date: new Date().toISOString().split('T')[0],
        verified: false
    };
    
    reviews.push(newReview);
    
    if (writeJSONFile(REVIEWS_FILE, reviews)) {
        res.status(201).json(newReview);
    } else {
        res.status(500).json({ error: 'Failed to save review' });
    }
});

// Blogs API
app.get('/api/blogs', (req, res) => {
    const blogs = readJSONFile(BLOGS_FILE);
    const publishedBlogs = blogs.filter(b => b.published);
    res.json(publishedBlogs);
});

// Customers API
app.get('/api/customers', (req, res) => {
    const customers = readJSONFile(CUSTOMERS_FILE);
    res.json(customers);
});

// Start server
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
    console.log(`Admin panel: http://localhost:${PORT}/admin`);
});
