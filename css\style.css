@import url('https://fonts.googleapis.com/css2?family=Comfortaa:wght@300..700&family=Roboto:ital,wght@0,100..900;1,100..900&family=Share+Tech&family=Tagesschrift&display=swap');

:root
{
	/* Professional Grocery Store Color Palette */
	--primary-green: #2E8B57;
	--secondary-green: #3CB371;
	--accent-green: #90EE90;
	--dark-green: #006400;
	--light-green: #F0FFF0;

	--primary-orange: #FF8C00;
	--secondary-orange: #FFA500;
	--light-orange: #FFE4B5;

	--black: #1a1a1a;
	--dark-gray: #2c2c2c;
	--medium-gray: #666;
	--light-gray: #f8f9fa;
	--white: #ffffff;

	--text-primary: #2c2c2c;
	--text-secondary: #666;
	--text-light: #999;

	/* Enhanced Shadows and Effects */
	--box-shadow: 0 0.5rem 1.5rem rgba(0,0,0,0.1);
	--box-shadow-hover: 0 1rem 3rem rgba(0,0,0,0.15);
	--box-shadow-active: 0 0.25rem 0.5rem rgba(0,0,0,0.1);

	/* Glowing Effects */
	--glow-primary: 0 0 20px rgba(46, 139, 87, 0.3);
	--glow-secondary: 0 0 30px rgba(46, 139, 87, 0.5);
	--glow-orange: 0 0 20px rgba(255, 140, 0, 0.4);

	--border: 0.2rem solid rgba(0,0,0,0.1);
	--outline: 0.1rem solid rgba(0,0,0,0.1);

	/* Animation Variables */
	--transition-fast: all 0.2s ease;
	--transition-normal: all 0.3s ease;
	--transition-slow: all 0.5s ease;
}

*
{
	 font-family: "Roboto", sans-serif;
	 margin: 0;
	 padding: 0;
	 box-sizing: border-box;
	 outline: none;
	 border: none;
	 text-decoration: none;
	 text-transform: capitalize;
	 transition: all .2s linear;
}

html
{
	font-size: 62.5%;
	overflow-x: hidden;
	scroll-behavior: smooth;
	scroll-padding-top: 7rem;
}

body
{
	background: #eee;
}


/*css code for banner*/
section
{
	padding: 2rem 9%;
}

.heading
{
	text-align: center;
	padding: 2rem 0;
	padding-bottom: 3rem;
	font-size: 3rem;
	color: var(--black);
}

.heading span
{
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	color: var(--white);
	display: inline-block;
	padding: .8rem 3rem;
	clip-path: polygon(100% 0, 93% 50%, 100% 99%, 0% 100%, 7% 50%, 0% 0%);
	box-shadow: var(--glow-primary);
	animation: glow 2s ease-in-out infinite alternate;
}

.btn
{
	border: 2px solid var(--primary-green);
	margin-top: 1rem;
	display: inline-block;
	padding: 1.2rem 3rem;
	font-size: 1.6rem;
	font-weight: 600;
	border-radius: 3rem;
	color: var(--primary-green);
	cursor: pointer;
	background: transparent;
	text-decoration: none;
	text-transform: uppercase;
	letter-spacing: 1px;
	transition: var(--transition-normal);
	position: relative;
	overflow: hidden;
	z-index: 1;
}

.btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	transition: var(--transition-normal);
	z-index: -1;
}

.btn:hover::before {
	left: 0;
}

.btn:hover
{
	color: var(--white);
	border-color: var(--secondary-green);
	transform: translateY(-2px);
	box-shadow: var(--glow-primary);
}

.btn.btn-primary {
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	color: var(--white);
	border-color: transparent;
}

.btn.btn-primary:hover {
	background: linear-gradient(45deg, var(--secondary-green), var(--primary-green));
	transform: translateY(-3px) scale(1.05);
	box-shadow: var(--glow-secondary);
}

.btn.btn-orange {
	background: linear-gradient(45deg, var(--primary-orange), var(--secondary-orange));
	color: var(--white);
	border-color: transparent;
}

.btn.btn-orange:hover {
	background: linear-gradient(45deg, var(--secondary-orange), var(--primary-orange));
	transform: translateY(-3px) scale(1.05);
	box-shadow: var(--glow-orange);
}

.header
{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 1.5rem 9%;
	background: linear-gradient(135deg, var(--white) 0%, var(--light-gray) 100%);
	backdrop-filter: blur(10px);
	box-shadow: var(--box-shadow);
	z-index: 1000;
	transition: var(--transition-normal);
	border-bottom: 2px solid transparent;
}

.header.scrolled {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(15px);
	box-shadow: var(--box-shadow-hover);
	border-bottom: 2px solid var(--primary-green);
}

.header .logo
{
	font-size: 2.8rem;
	font-weight: 800;
	color: var(--text-primary);
	text-decoration: none;
	display: flex;
	align-items: center;
	gap: 1rem;
	transition: var(--transition-normal);
	position: relative;
}

.header .logo:hover {
	transform: scale(1.05);
	filter: drop-shadow(var(--glow-primary));
}

.header .logo i
{
	color: var(--primary-green);
	font-size: 3.2rem;
	animation: pulse 2s infinite;
}

@keyframes pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.1); }
}

.header .logo-img {
    height: 4.5rem;
    width: auto;
    border-radius: 1rem;
    object-fit: contain;
    transition: var(--transition-normal);
    box-shadow: 0 4px 15px rgba(46, 139, 87, 0.2);
}

.header .logo-img:hover {
	transform: rotate(5deg) scale(1.1);
	box-shadow: var(--glow-primary);
}

.header .logo span {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header .navbar {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.header .navbar a
{
	font-size: 1.6rem;
	font-weight: 600;
	margin: 0 0.8rem;
	color: var(--text-primary);
	text-decoration: none;
	padding: 1rem 1.5rem;
	border-radius: 2rem;
	position: relative;
	transition: var(--transition-normal);
	text-transform: uppercase;
	letter-spacing: 0.5px;
	overflow: hidden;
}

.header .navbar a::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(46, 139, 87, 0.1), transparent);
	transition: var(--transition-slow);
}

.header .navbar a:hover::before {
	left: 100%;
}

.header .navbar a:hover,
.header .navbar a.active
{
	color: var(--white);
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	box-shadow: var(--glow-primary);
	transform: translateY(-2px);
}

.header .navbar a.active {
	background: linear-gradient(45deg, var(--secondary-green), var(--primary-green));
	box-shadow: var(--glow-secondary);
}

.header .navbar .login-link
{
	background: linear-gradient(45deg, var(--primary-orange), var(--secondary-orange)) !important;
	color: var(--white) !important;
	padding: 1rem 2rem !important;
	border-radius: 2.5rem !important;
	transition: var(--transition-normal) !important;
	font-weight: 700 !important;
	text-transform: uppercase !important;
	letter-spacing: 1px !important;
	box-shadow: 0 4px 15px rgba(255, 140, 0, 0.3) !important;
	position: relative !important;
	overflow: hidden !important;
}

.header .navbar .login-link::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
	transition: var(--transition-slow);
}

.header .navbar .login-link:hover::before {
	left: 100%;
}

.header .navbar .login-link:hover
{
	background: linear-gradient(45deg, var(--secondary-orange), var(--primary-orange)) !important;
	transform: translateY(-3px) scale(1.05) !important;
	box-shadow: var(--glow-orange) !important;
}

/* Admin Panel Separate Section */
.admin-section {
	display: flex;
	align-items: center;
	gap: 1rem;
	margin-left: auto;
	padding-left: 2rem;
	border-left: 2px solid #e1e5e9;
}

.admin-panel-link {
	background: linear-gradient(45deg, #ff6b6b, #ee5a24) !important;
	color: white !important;
	padding: 1rem 2rem !important;
	border-radius: 2rem !important;
	font-weight: 600 !important;
	text-transform: uppercase !important;
	letter-spacing: 1px !important;
	box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
	transition: all 0.3s ease !important;
	position: relative !important;
	overflow: hidden !important;
}

.admin-panel-link:before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
	transition: left 0.5s;
}

.admin-panel-link:hover:before {
	left: 100%;
}

.admin-panel-link:hover {
	background: linear-gradient(45deg, #ee5a24, #ff6b6b) !important;
	transform: translateY(-3px) !important;
	box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4) !important;
}

.admin-panel-link i {
	margin-right: 0.5rem;
	font-size: 1.2rem;
}

.header .icons {
	display: flex;
	align-items: center;
	gap: 0.5rem;
}

.header .icons div
{
	height: 4.5rem;
	width: 4.5rem;
	line-height: 4.5rem;
	border-radius: 50%;
	background: linear-gradient(135deg, var(--light-gray), var(--white));
	color: var(--text-primary);
	font-size: 2rem;
	text-align: center;
	cursor: pointer;
	position: relative;
	transition: var(--transition-normal);
	border: 2px solid transparent;
	box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header .icons div::before {
	content: '';
	position: absolute;
	top: -2px;
	left: -2px;
	right: -2px;
	bottom: -2px;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green), var(--primary-orange));
	border-radius: 50%;
	z-index: -1;
	opacity: 0;
	transition: var(--transition-normal);
}

.header .icons div:hover::before {
	opacity: 1;
	animation: rotate 2s linear infinite;
}

@keyframes rotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.header .icons div:hover
{
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	color: var(--white);
	transform: translateY(-3px) scale(1.1);
	box-shadow: var(--glow-primary);
}

/* Cart Badge */
.cart-badge {
    position: absolute;
    top: -0.8rem;
    right: -0.8rem;
    background: #ff4757;
    color: white;
    border-radius: 50%;
    width: 2.2rem;
    height: 2.2rem;
    display: none;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    animation: bounce 0.3s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}



#menu-btn
{
	display: none;
}

.header .navbar.active
{
	right: 2rem;
	transition: .4s linear;
}

.header .search-form
{
	position: absolute;
	top: 120%;
	right: -110%;
	width: 50rem;
	height: 6rem;
	background: var(--white);
	border-radius: 3rem;
	overflow: hidden;
	display: flex;
	align-items: center;
	box-shadow: var(--box-shadow-hover);
	border: 2px solid var(--primary-green);
	transition: var(--transition-normal);
}

.header .search-form.active
{
	right: 2rem;
	transition: var(--transition-normal);
	transform: scale(1.02);
	box-shadow: var(--glow-primary);
	border-color: var(--secondary-green);
}


.header .search-form input
{
	height: 100%;
	width: 100%;
	background: transparent;
	text-transform: none;
	font-size: 1.7rem;
	color: var(--text-primary);
	padding: 0 2rem;
	border: none;
	outline: none;
}

.header .search-form input::placeholder {
	color: var(--text-secondary);
	opacity: 0.7;
}

.header .search-form label
{
	font-size: 2.2rem;
	padding-right: 1.5rem;
	color: var(--primary-green);
	cursor: pointer;
	transition: var(--transition-normal);
	display: flex;
	align-items: center;
	justify-content: center;
	min-width: 5rem;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	opacity: 1;
	visibility: visible;
}

.header .search-form label:hover
{
	color: var(--secondary-green);
	transform: scale(1.1);
	filter: drop-shadow(var(--glow-primary));
}

.search-icon {
	position: relative;
	z-index: 10;
	display: block !important;
	visibility: visible !important;
	opacity: 1 !important;
}

.search-icon::before {
	content: '\f002';
	font-family: 'Font Awesome 6 Free';
	font-weight: 900;
	color: var(--primary-green);
	font-size: 2.2rem;
}



.header .shopping-cart
{
	border: 1px solid;
	position: absolute;
	top: 110%;
	right: -110%;
	padding: 1rem;
	border-radius: .5rem;
	box-shadow: var(--box-shadow);
	width: 35rem;
	background: #fff;
}


.header .shopping-cart.active
{
	right: 2rem;
	transition: .4s linear;
}


.header .shopping-cart .box
{
	border: 0px solid blue;
	display: flex;
	align-items: center;
	gap: 1rem;
	position: relative;
	margin: 1rem 0;
}

.header .shopping-cart .box img
{
	height: 10rem;
}

.header .shopping-cart .box .fa-trash
{
	font-size: 2rem;
	position: absolute;
	top: 50%;
	right: 2rem;
	cursor: pointer;
	color: var(--light-color);
	transform: translate(-50%);
}

.header .shopping-cart .box .fa-trash:hover
{
	color: var(--green);
}

.header .shopping-cart .box .content h3
{
	color: var(--black);
	font-size: 1.8rem;
	padding-bottom: 1rem;
}

.header .shopping-cart .box .content span
{
	color: var(--light-color);
	font-size: 1.6rem;
}

.header .shopping-cart .box .content quantity
{
	padding-left: 1rem;
}

.header .shopping-cart .Total
{
	font-size: 2.5rem;
	padding: 1rem 0;
	text-align: center;
	color: var(--black);
}



.header .shopping-cart .btn 
{
	display: block;
	text-align: center;
	margin: 1rem 0;
}


 .header .login-form
{
	border: 0px solid;
	position: absolute;
	width: 35rem;
	top: 110%;
	right: -110%;
	box-shadow: var(--box-shadow);
	padding: 2rem;
	border-radius: .5rem;
	background: #fff;
	text-align: center;
}

.header .login-form.active
{
	right: 2rem;
	transition: .4s linear;
}

.header .login-form h3
{
	font-size: 2.6rem;
	text-transform: uppercase;
	color: var(--black);
}





.header .login-form .box 
{
	width: 100%;
	border: 0px solid;
	margin: .7rem 0;
	background: #eee;
	border-radius: .5rem;
	padding: 1rem;
	font-size: 1.6rem;
	color: var(--black);
	text-transform: none;
}






.header .login-form p 
{
	font-size: 1.5rem;
	padding: .5rem 0;
    color: var(--light-color);
}

.header .login-form p a 
{
	color: var(--green);
	text-decoration: underline;
}



.home
{
    display: flex;
	justify-content: center;
	align-items: center;
	background: linear-gradient(135deg, rgba(46, 139, 87, 0.9), rgba(60, 179, 113, 0.8)), url(../image/banner-img.jpg) no-repeat;
	background-position: center;
	background-size: cover;
	background-attachment: fixed;
	padding-top: 20rem;
	padding-bottom: 15rem;
	position: relative;
	overflow: hidden;
}

.home::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
	animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
	0%, 100% { transform: translateX(-100%); }
	50% { transform: translateX(100%); }
}

.home .content
{
	text-align: center;
	width: 70rem;
	max-width: 90%;
	position: relative;
	z-index: 2;
	animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
	0% {
		opacity: 0;
		transform: translateY(50px);
	}
	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.home .content h3
{
	color: var(--white);
	font-size: 4.5rem;
	font-weight: 800;
	margin-bottom: 2rem;
	text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
	line-height: 1.2;
}

.home .content h3 span
{
	color: var(--light-orange);
	text-shadow: 0 0 20px rgba(255, 140, 0, 0.5);
	animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
	from { text-shadow: 0 0 20px rgba(255, 140, 0, 0.5); }
	to { text-shadow: 0 0 30px rgba(255, 140, 0, 0.8), 0 0 40px rgba(255, 140, 0, 0.6); }
}

.home .content p
{
	color: var(--white);
	font-size: 2rem;
    padding: 2rem 0;
    line-height: 1.8;
	text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
	opacity: 0.95;
}


/*features*/
.features {
	background: linear-gradient(135deg, var(--light-green), var(--white));
	position: relative;
}

.features::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: radial-gradient(circle at 20% 50%, rgba(46, 139, 87, 0.05) 0%, transparent 50%),
	            radial-gradient(circle at 80% 20%, rgba(60, 179, 113, 0.05) 0%, transparent 50%),
	            radial-gradient(circle at 40% 80%, rgba(46, 139, 87, 0.03) 0%, transparent 50%);
	opacity: 0.8;
}

.features .box-container
{
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(35rem, 1fr));
    gap: 3rem;
	position: relative;
	z-index: 2;
}

.features .box-container .box
{
	padding: 4rem 3rem;
	background: var(--white);
    border-radius: 2rem;
    text-align: center;
    box-shadow: var(--box-shadow);
	transition: var(--transition-normal);
	position: relative;
	overflow: hidden;
	border: 2px solid transparent;
}

.features .box-container .box::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	opacity: 0;
	transition: var(--transition-normal);
	z-index: -1;
}

.features .box-container .box:hover::before {
	opacity: 0.05;
}

.features .box-container .box:hover
{
	transform: translateY(-10px) scale(1.02);
	box-shadow: var(--box-shadow-hover);
	border-color: var(--primary-green);
}

 .features .box-container .box img
{
	margin: 2rem 0;
	height: 18rem;
	transition: var(--transition-normal);
	filter: drop-shadow(0 5px 15px rgba(46, 139, 87, 0.2));
}

.features .box-container .box:hover img {
	transform: scale(1.1) rotate(5deg);
	filter: drop-shadow(var(--glow-primary));
}

.features .box-container .box h3
{
	font-size: 2.4rem;
	font-weight: 700;
	line-height: 1.4;
	color: var(--text-primary);
	margin-bottom: 1.5rem;
	transition: var(--transition-normal);
}

.features .box-container .box:hover h3 {
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
	transform: scale(1.05);
}

.features .box-container .box p
{
	font-size: 1.5rem;
	line-height: 1.8;
	color: var(--text-secondary);
	margin-bottom: 2rem;
}

/*features*/



/*products*/
.products {
	background: var(--white);
	position: relative;
}

.products .product-slider
{
	padding: 2rem;
	position: relative;
}

.products .product-slider:first-child
{
	margin-bottom: 3rem;
}

.products .product-slider .box
{
	background: var(--white);
	border-radius: 2rem;
	text-align: center;
	padding: 3rem 2.5rem;
	box-shadow: var(--box-shadow);
	transition: var(--transition-normal);
	position: relative;
	overflow: hidden;
	border: 2px solid transparent;
	cursor: pointer;
}

.products .product-slider .box::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	opacity: 0;
	transition: var(--transition-normal);
	z-index: -1;
}

.products .product-slider .box:hover::before {
	opacity: 0.05;
}

.products .product-slider .box:hover
{
	transform: translateY(-15px) scale(1.05);
	box-shadow: var(--box-shadow-hover);
	border-color: var(--primary-green);
}

.products .product-slider .box img
{
	height: 22rem;
	transition: var(--transition-normal);
	filter: drop-shadow(0 5px 15px rgba(0,0,0,0.1));
}

.products .product-slider .box:hover img {
	transform: scale(1.1);
	filter: drop-shadow(0 10px 25px rgba(46, 139, 87, 0.3));
}

.products .product-slider .box h1
{
	font-size: 3rem;
	color: var(--text-primary);
	margin: 1.5rem 0;
}

.products .product-slider .box .price
{
	font-size: 2.2rem;
	font-weight: 700;
	color: var(--primary-green);
	padding: 1rem 0;
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.products .product-slider .box .stars {
	margin: 1rem 0;
}

.products .product-slider .box .stars i
{
	font-size: 1.8rem;
	color: var(--primary-orange);
	margin: 0 0.2rem;
	transition: var(--transition-fast);
}

.products .product-slider .box:hover .stars i {
	color: var(--secondary-orange);
	transform: scale(1.2);
}

.products .product-slider .box h3
{
	font-size: 2.2rem;
	font-weight: 600;
	color: var(--text-primary);
	margin: 1.5rem 0;
	transition: var(--transition-normal);
}

.products .product-slider .box:hover h3 {
	color: var(--primary-green);
}

/* Categories page styles */
.categories .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
	gap: 2rem;
}

.categories .box-container .box
{
	padding: 3rem 2rem;
	background: #fff;
	outline: var(--outline);
	outline-offset: -1rem;
	text-align: center;
	box-shadow: var(--box-shadow);
	border-radius: 1rem;
	transition: all 0.3s ease;
}

.categories .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.categories .box-container .box img
{
	margin: 1rem 0;
	height: 15rem;
}

.categories .box-container .box h3
{
	font-size: 2rem;
	color: var(--black);
	margin: 1rem 0;
}

.categories .box-container .box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
}

/* Featured products section */
.featured-products
{
	background: #fff;
	padding: 2rem 9%;
	position: relative;
}

.products-container {
	position: relative;
}

.scroll-controls {
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin-top: 2rem;
}

.scroll-btn {
	background: var(--green);
	color: white;
	border: none;
	width: 4rem;
	height: 4rem;
	border-radius: 50%;
	cursor: pointer;
	font-size: 1.8rem;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.scroll-btn:hover {
	background: #006400;
	transform: scale(1.1);
}

.scroll-btn:disabled {
	background: #ccc;
	cursor: not-allowed;
	transform: none;
}

.category-tabs
{
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin-bottom: 3rem;
	flex-wrap: wrap;
}

.category-tabs .tab-btn
{
	background: none;
	border: 2px solid var(--green);
	padding: 1rem 2rem;
	font-size: 1.6rem;
	color: var(--green);
	cursor: pointer;
	border-radius: 0.5rem;
	transition: all 0.3s ease;
}

.category-tabs .tab-btn.active,
.category-tabs .tab-btn:hover
{
	background: var(--green);
	color: white;
}

.products-grid
{
	display: flex;
	overflow-x: auto;
	gap: 2rem;
	padding: 1rem 0;
	scroll-behavior: smooth;
}

.products-grid::-webkit-scrollbar {
	height: 8px;
}

.products-grid::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 10px;
}

.products-grid::-webkit-scrollbar-thumb {
	background: var(--green);
	border-radius: 10px;
}

.products-grid::-webkit-scrollbar-thumb:hover {
	background: #006400;
}

.product-box
{
	background: #fff;
	border-radius: 1rem;
	text-align: center;
	padding: 2rem;
	outline-offset: -1rem;
	outline: var(--outline);
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
	min-width: 25rem;
	flex-shrink: 0;
}

.product-box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.product-box img
{
	height: 15rem;
	margin-bottom: 1rem;
}

.product-box h3
{
	font-size: 1.8rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.product-box .price
{
	font-size: 2rem;
	color: var(--light-color);
	margin-bottom: 1rem;
}

.product-box .stars
{
	margin-bottom: 1rem;
}

.product-box .stars i
{
	font-size: 1.5rem;
	color: orange;
}

/* Reviews page styles */
.reviews .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(30rem, 1fr));
	gap: 2rem;
}

.reviews .box-container .box
{
	padding: 3rem 2rem;
	background: #fff;
	outline: var(--outline);
	outline-offset: -1rem;
	text-align: center;
	box-shadow: var(--box-shadow);
	border-radius: 1rem;
	transition: all 0.3s ease;
}

.reviews .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.reviews .box-container .box img
{
	height: 10rem;
	width: 10rem;
	border-radius: 50%;
	object-fit: cover;
	margin-bottom: 1rem;
}

.reviews .box-container .box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
	font-style: italic;
}

.reviews .box-container .box .stars
{
	margin-bottom: 1rem;
}

.reviews .box-container .box .stars i
{
	font-size: 1.5rem;
	color: orange;
}

.reviews .box-container .box h3
{
	font-size: 1.8rem;
	color: var(--black);
	margin-bottom: 0.5rem;
}

.reviews .box-container .box span
{
	font-size: 1.2rem;
	color: var(--green);
	text-transform: lowercase;
}

/* Review stats */
.review-stats
{
	background: var(--green);
	padding: 4rem 9%;
}

.review-stats .heading
{
	color: white;
}

.stats-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
	gap: 2rem;
}

.stat-box
{
	background: white;
	padding: 3rem 2rem;
	text-align: center;
	border-radius: 1rem;
	transition: transform 0.3s ease;
}

.stat-box:hover
{
	transform: translateY(-5px);
}

.stat-box i
{
	font-size: 4rem;
	color: var(--green);
	margin-bottom: 1rem;
}

.stat-box h3
{
	font-size: 3rem;
	color: var(--black);
	margin-bottom: 0.5rem;
}

.stat-box p
{
	font-size: 1.4rem;
	color: var(--light-color);
}

/* Write review section */
.write-review
{
	background: #f8f9fa;
	padding: 4rem 9%;
}

.review-form
{
	max-width: 60rem;
	margin: 0 auto;
	background: white;
	padding: 3rem;
	border-radius: 1rem;
	box-shadow: var(--box-shadow);
}

.input-group
{
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 1rem;
	margin-bottom: 2rem;
}

.input-group input
{
	padding: 1rem;
	border: 1px solid #ddd;
	border-radius: 0.5rem;
	font-size: 1.4rem;
}

.rating-input
{
	margin-bottom: 2rem;
}

.rating-input label
{
	display: block;
	margin-bottom: 1rem;
	font-size: 1.6rem;
	color: var(--black);
	font-weight: 600;
}

.stars-input
{
	display: flex;
	gap: 0.5rem;
}

.stars-input i
{
	font-size: 2rem;
	color: #ddd;
	cursor: pointer;
	transition: color 0.3s ease;
}

.stars-input i:hover,
.stars-input i.fas
{
	color: orange;
}

.review-form textarea
{
	width: 100%;
	padding: 1rem;
	border: 1px solid #ddd;
	border-radius: 0.5rem;
	font-size: 1.4rem;
	resize: vertical;
	margin-bottom: 2rem;
}

/* Blogs page styles */
.blogs .box-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(35rem, 1fr));
	gap: 2rem;
}

.blogs .box-container .box
{
	background: #fff;
	border-radius: 1rem;
	overflow: hidden;
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
}

.blogs .box-container .box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.blogs .box-container .box img
{
	width: 100%;
	height: 20rem;
	object-fit: cover;
}

.blogs .box-container .box .content
{
	padding: 2rem;
}

.blogs .box-container .box .content .icons
{
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 1rem;
}

.blogs .box-container .box .content .icons a
{
	font-size: 1.2rem;
	color: var(--light-color);
	text-decoration: none;
}

.blogs .box-container .box .content .icons a:hover
{
	color: var(--green);
}

.blogs .box-container .box .content .icons i
{
	margin-right: 0.5rem;
}

.blogs .box-container .box .content h3
{
	font-size: 2rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.blogs .box-container .box .content p
{
	font-size: 1.4rem;
	color: var(--light-color);
	line-height: 1.8;
	margin-bottom: 1rem;
}

/* Blog categories */
.blog-categories
{
	background: #f8f9fa;
	padding: 4rem 9%;
}

.category-container
{
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
	gap: 2rem;
}

.category-box
{
	background: white;
	padding: 3rem 2rem;
	text-align: center;
	border-radius: 1rem;
	box-shadow: var(--box-shadow);
	transition: all 0.3s ease;
}

.category-box:hover
{
	box-shadow: 1px 1px 10px 4px var(--green);
	transform: translateY(-5px);
}

.category-box i
{
	font-size: 4rem;
	color: var(--green);
	margin-bottom: 1rem;
}

.category-box h3
{
	font-size: 2rem;
	color: var(--black);
	margin-bottom: 1rem;
}

.category-box p
{
	font-size: 1.4rem;
	color: var(--light-color);
	margin-bottom: 1rem;
}

.category-box .post-count
{
	font-size: 1.2rem;
	color: var(--green);
	font-weight: 600;
}

/* Newsletter section */
.newsletter
{
	background: var(--green);
	padding: 4rem 9%;
	text-align: center;
}

.newsletter .heading
{
	color: white;
}

.newsletter-content p
{
	color: white;
	font-size: 1.6rem;
	margin-bottom: 2rem;
	max-width: 60rem;
	margin-left: auto;
	margin-right: auto;
}

.newsletter-form
{
	display: flex;
	max-width: 50rem;
	margin: 0 auto 2rem;
	gap: 1rem;
}

.newsletter-form input[type="email"]
{
	flex: 1;
	padding: 1rem;
	border: none;
	border-radius: 0.5rem;
	font-size: 1.4rem;
}

.newsletter-form .btn
{
	background: var(--black);
	color: white;
	border: none;
}

.social-links
{
	display: flex;
	justify-content: center;
	gap: 1rem;
}

.social-links a
{
	display: inline-block;
	width: 4rem;
	height: 4rem;
	background: white;
	color: var(--green);
	text-align: center;
	line-height: 4rem;
	border-radius: 50%;
	font-size: 1.8rem;
	transition: all 0.3s ease;
}

.social-links a:hover
{
	background: var(--black);
	color: white;
	transform: translateY(-3px);
}

/* Responsive design */
@media (max-width: 991px) {
	html {
		font-size: 55%;
	}

	.header {
		padding: 2rem;
	}

	section {
		padding: 2rem;
	}
}

@media (max-width: 768px) {
	#menu-btn {
		display: inline-block;
	}

	.header .navbar {
		position: absolute;
		top: 110%;
		right: -110%;
		width: 30rem;
		box-shadow: var(--box-shadow);
		border-radius: .5rem;
		background: #fff;
		flex-direction: column;
	}

	.header .navbar.active {
		right: 2rem;
	}

	.header .navbar a {
		font-size: 2rem;
		margin: 2rem 2.5rem;
		display: block;
	}

	.admin-section {
		margin-left: 0;
		padding-left: 0;
		border-left: none;
		border-top: 2px solid #e1e5e9;
		padding-top: 2rem;
		margin-top: 2rem;
	}

	.admin-panel-link {
		font-size: 1.8rem !important;
		padding: 1.5rem 2rem !important;
		margin: 0 2.5rem !important;
		text-align: center !important;
	}

	.input-group {
		grid-template-columns: 1fr;
	}

	.newsletter-form {
		flex-direction: column;
	}

	.products-grid {
		padding: 1rem;
	}

	.scroll-controls {
		margin-top: 1rem;
	}
}

@media (max-width: 450px) {
	html {
		font-size: 50%;
	}
}











/* Professional Animations and Effects */

/* Floating Animation */
@keyframes float {
	0%, 100% { transform: translateY(0px); }
	50% { transform: translateY(-10px); }
}

.float-animation {
	animation: float 3s ease-in-out infinite;
}

/* Slide In Animations */
@keyframes slideInLeft {
	0% {
		opacity: 0;
		transform: translateX(-50px);
	}
	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInRight {
	0% {
		opacity: 0;
		transform: translateX(50px);
	}
	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

/* Scale Animation */
@keyframes scaleIn {
	0% {
		opacity: 0;
		transform: scale(0.8);
	}
	100% {
		opacity: 1;
		transform: scale(1);
	}
}

/* Notification Animations */
@keyframes slideIn {
	0% {
		opacity: 0;
		transform: translateX(100%);
	}
	100% {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideOut {
	0% {
		opacity: 1;
		transform: translateX(0);
	}
	100% {
		opacity: 0;
		transform: translateX(100%);
	}
}

/* Loading Animation */
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-spinner {
	border: 4px solid var(--light-gray);
	border-top: 4px solid var(--primary-green);
	border-radius: 50%;
	width: 40px;
	height: 40px;
	animation: spin 1s linear infinite;
	margin: 2rem auto;
}

/* Gradient Background Animation */
@keyframes gradientShift {
	0% { background-position: 0% 50%; }
	50% { background-position: 100% 50%; }
	100% { background-position: 0% 50%; }
}

.animated-gradient {
	background: linear-gradient(-45deg, var(--primary-green), var(--secondary-green), var(--primary-orange), var(--secondary-orange));
	background-size: 400% 400%;
	animation: gradientShift 15s ease infinite;
}

/* Scroll Reveal Animation */
.reveal {
	opacity: 0;
	transform: translateY(50px);
	transition: all 0.6s ease;
}

.reveal.active {
	opacity: 1;
	transform: translateY(0);
}

/* Enhanced Hover Effects */
.hover-lift {
	transition: var(--transition-normal);
}

.hover-lift:hover {
	transform: translateY(-5px);
	box-shadow: var(--box-shadow-hover);
}

/* Glowing Border Animation */
@keyframes glowBorder {
	0%, 100% {
		box-shadow: 0 0 5px var(--primary-green);
	}
	50% {
		box-shadow: 0 0 20px var(--primary-green), 0 0 30px var(--primary-green);
	}
}

.glow-border {
	animation: glowBorder 2s ease-in-out infinite;
}

/* Professional Scrollbar */
::-webkit-scrollbar {
	width: 12px;
}

::-webkit-scrollbar-track {
	background: var(--light-gray);
	border-radius: 10px;
}

::-webkit-scrollbar-thumb {
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	border-radius: 10px;
	border: 2px solid var(--light-gray);
}

::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(45deg, var(--secondary-green), var(--primary-green));
}

/* Testimonials Section */
.testimonials {
	background: linear-gradient(135deg, var(--light-gray), var(--white));
	padding: 8rem 9%;
	position: relative;
}

.testimonials-container {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(35rem, 1fr));
	gap: 3rem;
	margin-top: 4rem;
}

.testimonial-box {
	background: var(--white);
	padding: 4rem 3rem;
	border-radius: 2rem;
	box-shadow: var(--box-shadow);
	text-align: center;
	position: relative;
	border: 2px solid transparent;
	transition: var(--transition-normal);
}

.testimonial-box::before {
	content: '"';
	position: absolute;
	top: -2rem;
	left: 50%;
	transform: translateX(-50%);
	font-size: 8rem;
	color: var(--primary-green);
	opacity: 0.1;
	font-family: serif;
}

.testimonial-box:hover {
	border-color: var(--primary-green);
	box-shadow: var(--box-shadow-hover);
}

.customer-info {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 1.5rem;
	margin-bottom: 2rem;
}

.customer-info img {
	width: 6rem;
	height: 6rem;
	border-radius: 50%;
	object-fit: cover;
	border: 3px solid var(--primary-green);
}

.customer-details h4 {
	font-size: 1.8rem;
	color: var(--text-primary);
	margin-bottom: 0.5rem;
}

.customer-details span {
	font-size: 1.4rem;
	color: var(--text-secondary);
}

.testimonial-box p {
	font-size: 1.6rem;
	line-height: 1.8;
	color: var(--text-secondary);
	margin-bottom: 2rem;
	font-style: italic;
}

.testimonial-box .stars {
	display: flex;
	justify-content: center;
	gap: 0.5rem;
}

.testimonial-box .stars i {
	font-size: 1.8rem;
	color: var(--primary-orange);
}

/* Newsletter Section */
.newsletter {
	padding: 8rem 9%;
	text-align: center;
	color: var(--white);
}

.newsletter .heading {
	color: var(--white);
	margin-bottom: 2rem;
}

.newsletter-content p {
	font-size: 1.8rem;
	margin-bottom: 4rem;
	max-width: 70rem;
	margin-left: auto;
	margin-right: auto;
	line-height: 1.8;
}

.newsletter-form {
	display: flex;
	max-width: 60rem;
	margin: 0 auto 4rem;
	gap: 1rem;
	background: rgba(255, 255, 255, 0.1);
	padding: 1rem;
	border-radius: 5rem;
	backdrop-filter: blur(10px);
}

.newsletter-form input[type="email"] {
	flex: 1;
	padding: 1.5rem 2rem;
	border: none;
	border-radius: 5rem;
	font-size: 1.6rem;
	background: rgba(255, 255, 255, 0.9);
	color: var(--text-primary);
}

.newsletter-form input[type="email"]:focus {
	outline: none;
	box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.newsletter-form .btn {
	margin: 0;
	border-radius: 5rem;
	padding: 1.5rem 3rem;
	white-space: nowrap;
}

.social-links {
	display: flex;
	justify-content: center;
	gap: 2rem;
}

.social-links a {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 5rem;
	height: 5rem;
	background: rgba(255, 255, 255, 0.1);
	color: var(--white);
	border-radius: 50%;
	font-size: 2rem;
	transition: var(--transition-normal);
	backdrop-filter: blur(10px);
	border: 2px solid rgba(255, 255, 255, 0.2);
}

.social-links a:hover {
	background: var(--white);
	color: var(--primary-green);
	transform: translateY(-5px) scale(1.1);
	box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Footer Section */
.footer {
	background: linear-gradient(135deg, var(--dark-gray), var(--black));
	color: var(--white);
	padding: 6rem 9% 2rem;
}

.footer-content {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(25rem, 1fr));
	gap: 4rem;
	margin-bottom: 4rem;
}

.footer-section h3 {
	font-size: 2.4rem;
	margin-bottom: 2rem;
	color: var(--white);
	background: linear-gradient(45deg, var(--primary-green), var(--secondary-green));
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	background-clip: text;
}

.footer-section h4 {
	font-size: 1.8rem;
	margin-bottom: 1.5rem;
	color: var(--white);
}

.footer-section p {
	font-size: 1.4rem;
	line-height: 1.8;
	color: #ccc;
	margin-bottom: 2rem;
}

.footer-section ul {
	list-style: none;
}

.footer-section ul li {
	margin-bottom: 1rem;
}

.footer-section ul li a {
	color: #ccc;
	text-decoration: none;
	font-size: 1.4rem;
	transition: var(--transition-normal);
}

.footer-section ul li a:hover {
	color: var(--primary-green);
	padding-left: 1rem;
}

.footer-social {
	display: flex;
	gap: 1rem;
	margin-top: 2rem;
}

.footer-social a {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 4rem;
	height: 4rem;
	background: var(--primary-green);
	color: var(--white);
	border-radius: 50%;
	font-size: 1.6rem;
	transition: var(--transition-normal);
}

.footer-social a:hover {
	background: var(--secondary-green);
	transform: translateY(-3px);
	box-shadow: var(--glow-primary);
}

.contact-info p {
	display: flex;
	align-items: center;
	gap: 1rem;
	margin-bottom: 1rem;
	font-size: 1.4rem;
	color: #ccc;
}

.contact-info i {
	color: var(--primary-green);
	font-size: 1.6rem;
}

.footer-bottom {
	text-align: center;
	padding-top: 2rem;
	border-top: 1px solid #444;
}

.footer-bottom p {
	font-size: 1.4rem;
	color: #999;
}

/* Responsive Design for New Sections */
@media (max-width: 768px) {
	.testimonials-container {
		grid-template-columns: 1fr;
		gap: 2rem;
	}

	.newsletter-form {
		flex-direction: column;
		gap: 1.5rem;
	}

	.newsletter-form .btn {
		align-self: center;
	}

	.social-links {
		gap: 1rem;
	}

	.footer-content {
		grid-template-columns: 1fr;
		gap: 3rem;
	}

	.customer-info {
		flex-direction: column;
		text-align: center;
	}

	.header .search-form {
		width: 90%;
		max-width: 35rem;
	}

	.search-icon {
		font-size: 2rem !important;
		min-width: 4rem !important;
	}
}

