// Authentication JavaScript

// User management
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let users = JSON.parse(localStorage.getItem('users')) || [
    {
        id: 1,
        firstName: 'Demo',
        lastName: 'Customer',
        email: '<EMAIL>',
        password: 'demo123',
        phone: '+1234567890',
        address: '123 Main St, City, State 12345',
        role: 'customer',
        joinDate: '2024-01-01',
        isActive: true
    },
    {
        id: 2,
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'admin123',
        phone: '+1234567891',
        address: '456 Admin Ave, City, State 12345',
        role: 'admin',
        joinDate: '2024-01-01',
        isActive: true
    }
];

// Initialize authentication
document.addEventListener('DOMContentLoaded', function() {
    console.log('Auth system initializing...'); // Debug log

    // Load current user from localStorage
    currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
    window.currentUser = currentUser; // Make it globally available

    console.log('Current user loaded:', currentUser); // Debug log

    initializeAuth();
    updateUserInterface();
    updateMainPageUI();
});

function initializeAuth() {
    // Login form handler
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // Register form handler
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
        
        // Password strength checker
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('input', checkPasswordStrength);
        }
    }

    // User profile dropdown
    const loginBtn = document.getElementById('login-btn');
    const userProfile = document.getElementById('user-profile');
    
    if (loginBtn && userProfile) {
        loginBtn.addEventListener('click', toggleUserProfile);
    }

    // Logout handler
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.user-profile') && !e.target.closest('#login-btn')) {
            const userProfile = document.getElementById('user-profile');
            if (userProfile) {
                userProfile.classList.remove('active');
            }
        }
    });
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const email = formData.get('email');
    const password = formData.get('password');
    const rememberMe = formData.get('remember-me');

    console.log('Login attempt:', { email, password, rememberMe }); // Debug log

    // Validate input
    if (!email || !password) {
        showNotification('Please enter both email and password.', 'error');
        return;
    }

    // Find user
    const user = users.find(u => u.email === email && u.password === password);

    if (user) {
        if (!user.isActive) {
            showNotification('Account is deactivated. Please contact support.', 'error');
            return;
        }

        // Login successful
        currentUser = { ...user };
        delete currentUser.password; // Remove password from stored user data

        localStorage.setItem('currentUser', JSON.stringify(currentUser));

        if (rememberMe) {
            localStorage.setItem('rememberUser', 'true');
        }

        // Update global currentUser variable
        window.currentUser = currentUser;

        showNotification(`Welcome back, ${currentUser.firstName}!`, 'success');

        // Update UI immediately
        updateUserInterface();

        // Redirect based on role
        setTimeout(() => {
            if (user.role === 'admin') {
                window.location.href = 'admin.html';
            } else {
                window.location.href = 'index.html';
            }
        }, 1500);

    } else {
        showNotification('Invalid email or password. Please try again.', 'error');
        console.log('Available users:', users); // Debug log
    }
}

// Handle registration
async function handleRegister(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const userData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        address: formData.get('address'),
        newsletter: formData.get('newsletter') === 'on'
    };

    // Validation
    if (userData.password !== userData.confirmPassword) {
        showNotification('Passwords do not match!', 'error');
        return;
    }

    if (userData.password.length < 6) {
        showNotification('Password must be at least 6 characters long!', 'error');
        return;
    }

    // Check if email already exists
    if (users.find(u => u.email === userData.email)) {
        showNotification('Email already registered. Please use a different email.', 'error');
        return;
    }

    // Create new user
    const newUser = {
        id: users.length + 1,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        password: userData.password,
        address: userData.address,
        role: 'customer',
        joinDate: new Date().toISOString().split('T')[0],
        isActive: true,
        newsletter: userData.newsletter
    };

    users.push(newUser);
    localStorage.setItem('users', JSON.stringify(users));

    showNotification('Account created successfully! Please login.', 'success');
    
    setTimeout(() => {
        window.location.href = 'login.html';
    }, 2000);
}

// Toggle user profile dropdown
function toggleUserProfile() {
    const userProfile = document.getElementById('user-profile');
    if (userProfile) {
        userProfile.classList.toggle('active');
    }
}

// Handle logout
function handleLogout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('rememberUser');
    
    showNotification('Logged out successfully!', 'info');
    
    setTimeout(() => {
        window.location.href = 'index.html';
    }, 1500);
}

// Update user interface based on login status
function updateUserInterface() {
    const userNameElement = document.getElementById('user-name');
    const userEmailElement = document.getElementById('user-email');
    const loginBtn = document.getElementById('login-btn');
    const userProfile = document.getElementById('user-profile');

    console.log('Updating UI for user:', currentUser); // Debug log

    if (currentUser) {
        // User is logged in
        if (userNameElement) {
            userNameElement.textContent = `${currentUser.firstName} ${currentUser.lastName}`;
        }
        if (userEmailElement) {
            userEmailElement.textContent = currentUser.email;
        }

        // Update login button to show profile
        if (loginBtn) {
            loginBtn.innerHTML = '<i class="fas fa-user-circle"></i>';
            loginBtn.title = 'User Profile';
            loginBtn.onclick = null; // Remove redirect onclick
        }

        // Show user profile dropdown if available
        if (userProfile) {
            userProfile.style.display = 'block';
        }

        // Show admin panel link if user is admin
        const adminLink = document.getElementById('navbar-admin');
        const adminNavLink = document.getElementById('admin-nav-link');

        if (currentUser.role === 'admin') {
            if (adminLink) adminLink.style.display = 'block';
            if (adminNavLink) adminNavLink.style.display = 'inline-block';
            console.log('Admin links shown for admin user');
        } else {
            if (adminLink) adminLink.style.display = 'none';
            if (adminNavLink) adminNavLink.style.display = 'none';
            console.log('Admin links hidden for regular user');
        }

    } else {
        // User is not logged in
        if (userNameElement) {
            userNameElement.textContent = 'Guest User';
        }
        if (userEmailElement) {
            userEmailElement.textContent = 'Please login';
        }

        // Update login button
        if (loginBtn) {
            loginBtn.innerHTML = '<i class="fas fa-user"></i>';
            loginBtn.title = 'Login';
            loginBtn.onclick = () => window.location.href = 'login.html';
        }

        // Hide user profile dropdown
        if (userProfile) {
            userProfile.style.display = 'none';
        }

        // Hide admin panel links
        const adminLink = document.getElementById('navbar-admin');
        const adminNavLink = document.getElementById('admin-nav-link');

        if (adminLink) adminLink.style.display = 'none';
        if (adminNavLink) adminNavLink.style.display = 'none';

        console.log('Admin links hidden after logout');
    }
}

// Password toggle functionality
function togglePassword(inputId = 'password') {
    const passwordInput = document.getElementById(inputId);
    const eyeIcon = document.getElementById(inputId + '-eye');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Password strength checker
function checkPasswordStrength() {
    const password = document.getElementById('password').value;
    const strengthBar = document.getElementById('strength-bar');
    const strengthText = document.getElementById('strength-text');
    
    if (!strengthBar || !strengthText) return;
    
    let strength = 0;
    let feedback = '';
    
    // Length check
    if (password.length >= 8) strength += 25;
    
    // Uppercase check
    if (/[A-Z]/.test(password)) strength += 25;
    
    // Lowercase check
    if (/[a-z]/.test(password)) strength += 25;
    
    // Number or special character check
    if (/[\d\W]/.test(password)) strength += 25;
    
    // Update strength bar
    strengthBar.style.width = strength + '%';
    
    if (strength < 25) {
        strengthBar.style.background = '#ff4757';
        feedback = 'Very Weak';
    } else if (strength < 50) {
        strengthBar.style.background = '#ff6b7a';
        feedback = 'Weak';
    } else if (strength < 75) {
        strengthBar.style.background = '#ffa726';
        feedback = 'Medium';
    } else if (strength < 100) {
        strengthBar.style.background = '#66bb6a';
        feedback = 'Strong';
    } else {
        strengthBar.style.background = '#4caf50';
        feedback = 'Very Strong';
    }
    
    strengthText.textContent = `Password strength: ${feedback}`;
}

// Fill demo credentials
function fillDemoCredentials(type) {
    const emailInput = document.getElementById('email');
    const passwordInput = document.getElementById('password');
    
    if (type === 'customer') {
        emailInput.value = '<EMAIL>';
        passwordInput.value = 'demo123';
    } else if (type === 'admin') {
        emailInput.value = '<EMAIL>';
        passwordInput.value = 'admin123';
    }
    
    showNotification('Demo credentials filled!', 'info');
}

// Social login handlers (placeholder)
document.addEventListener('DOMContentLoaded', function() {
    const googleBtn = document.querySelector('.google-btn');
    const facebookBtn = document.querySelector('.facebook-btn');
    
    if (googleBtn) {
        googleBtn.addEventListener('click', function() {
            showNotification('Google login integration coming soon!', 'info');
        });
    }
    
    if (facebookBtn) {
        facebookBtn.addEventListener('click', function() {
            showNotification('Facebook login integration coming soon!', 'info');
        });
    }
});

// Check if user should be remembered
if (localStorage.getItem('rememberUser') && currentUser) {
    // Auto-login remembered user
    updateUserInterface();
}

// Quick login function for testing
function quickLogin(type) {
    let email, password;

    if (type === 'customer') {
        email = '<EMAIL>';
        password = 'demo123';
    } else if (type === 'admin') {
        email = '<EMAIL>';
        password = 'admin123';
    }

    // Find user
    const user = users.find(u => u.email === email && u.password === password);

    if (user) {
        // Login successful
        currentUser = { ...user };
        delete currentUser.password;

        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        window.currentUser = currentUser;

        showNotification(`Quick login successful! Welcome ${currentUser.firstName}!`, 'success');
        updateUserInterface();
        updateMainPageUI();
    }
}

// Update main page UI
function updateMainPageUI() {
    const loginStatus = document.getElementById('login-status');
    const userDetails = document.getElementById('user-details');
    const currentUserName = document.getElementById('current-user-name');
    const currentUserEmail = document.getElementById('current-user-email');
    const mainLoginBtn = document.getElementById('main-login-btn');
    const logoutMainBtn = document.getElementById('logout-main-btn');

    if (currentUser) {
        if (loginStatus) loginStatus.textContent = `Welcome, ${currentUser.firstName}!`;
        if (userDetails) userDetails.style.display = 'block';
        if (currentUserName) currentUserName.textContent = `${currentUser.firstName} ${currentUser.lastName}`;
        if (currentUserEmail) currentUserEmail.textContent = currentUser.email;
        if (mainLoginBtn) mainLoginBtn.style.display = 'none';
        if (logoutMainBtn) logoutMainBtn.style.display = 'inline-block';
    } else {
        if (loginStatus) loginStatus.textContent = 'Please Login';
        if (userDetails) userDetails.style.display = 'none';
        if (mainLoginBtn) mainLoginBtn.style.display = 'inline-block';
        if (logoutMainBtn) logoutMainBtn.style.display = 'none';
    }
}

// Enhanced logout function
function handleLogout() {
    currentUser = null;
    window.currentUser = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('rememberUser');

    showNotification('Logged out successfully!', 'info');
    updateUserInterface();
    updateMainPageUI();

    // Don't redirect, just update UI
    console.log('User logged out');
}

// Export functions for global use
window.currentUser = currentUser;
window.updateUserInterface = updateUserInterface;
window.handleLogout = handleLogout;
window.quickLogin = quickLogin;
window.updateMainPageUI = updateMainPageUI;
